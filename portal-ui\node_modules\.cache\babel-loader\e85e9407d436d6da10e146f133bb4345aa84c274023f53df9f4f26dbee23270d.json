{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport Layout from \"@/components/common/Layout\";\nexport default {\n  name: 'AboutView',\n  components: {\n    Layout\n  },\n  data() {\n    return {\n      connectionLines: []\n    };\n  },\n  mounted() {\n    this.generateConnectionLines();\n    this.startCounterAnimation();\n    this.optimizeForMobile();\n    this.handleOrientationChange();\n  },\n  methods: {\n    startTrial() {\n      this.$router.push('/product');\n    },\n    contactUs() {\n      // 可以添加联系我们的逻辑\n      console.log('联系我们');\n    },\n    getParticleStyle() {\n      return {\n        left: Math.random() * 100 + '%',\n        top: Math.random() * 100 + '%',\n        animationDelay: Math.random() * 10 + 's',\n        animationDuration: Math.random() * 20 + 10 + 's'\n      };\n    },\n    getNodeStyle() {\n      return {\n        left: Math.random() * 90 + 5 + '%',\n        top: Math.random() * 90 + 5 + '%',\n        animationDelay: Math.random() * 3 + 's'\n      };\n    },\n    generateConnectionLines() {\n      const lines = [];\n      for (let i = 0; i < 15; i++) {\n        lines.push({\n          id: i,\n          x1: Math.random() * 100,\n          y1: Math.random() * 100,\n          x2: Math.random() * 100,\n          y2: Math.random() * 100\n        });\n      }\n      this.connectionLines = lines;\n    },\n    startCounterAnimation() {\n      const counters = document.querySelectorAll('.stat-number');\n      counters.forEach(counter => {\n        const target = parseInt(counter.getAttribute('data-target'));\n        const increment = target / 100;\n        let current = 0;\n        const timer = setInterval(() => {\n          current += increment;\n          if (current >= target) {\n            counter.textContent = target;\n            clearInterval(timer);\n          } else {\n            counter.textContent = Math.floor(current);\n          }\n        }, 50);\n      });\n    },\n    // 移动端优化\n    optimizeForMobile() {\n      // 检测是否为移动设备\n      const isMobile = window.innerWidth <= 768;\n      if (isMobile) {\n        // 减少动画复杂度\n        this.reduceMobileAnimations();\n\n        // 优化触摸事件\n        this.optimizeTouchEvents();\n\n        // 预加载关键图片\n        this.preloadCriticalImages();\n      }\n    },\n    reduceMobileAnimations() {\n      // 减少粒子数量\n      const particles = document.querySelectorAll('.particle');\n      particles.forEach((particle, index) => {\n        if (index > 20) {\n          // 只保留前20个粒子\n          particle.style.display = 'none';\n        }\n      });\n\n      // 简化数据流动画\n      const dataStreams = document.querySelectorAll('.data-stream');\n      dataStreams.forEach((stream, index) => {\n        if (index > 4) {\n          // 只保留前4个数据流\n          stream.style.display = 'none';\n        }\n      });\n    },\n    optimizeTouchEvents() {\n      // 为移动端优化触摸反馈\n      const touchElements = document.querySelectorAll('.our_mission--item, .team-box, .contact-item, .am-btn');\n      touchElements.forEach(element => {\n        element.addEventListener('touchstart', () => {\n          element.style.transform = 'translateY(-2px)';\n        }, {\n          passive: true\n        });\n        element.addEventListener('touchend', () => {\n          setTimeout(() => {\n            element.style.transform = '';\n          }, 150);\n        }, {\n          passive: true\n        });\n      });\n    },\n    preloadCriticalImages() {\n      const criticalImages = ['/images/tiangonghead.jpeg', '/images/back1.webp'];\n      criticalImages.forEach(src => {\n        const img = new Image();\n        img.src = src;\n      });\n    },\n    handleOrientationChange() {\n      // 处理屏幕方向变化\n      window.addEventListener('orientationchange', () => {\n        setTimeout(() => {\n          // 重新计算布局\n          this.generateConnectionLines();\n\n          // 重新优化移动端设置\n          this.optimizeForMobile();\n        }, 500);\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["Layout", "name", "components", "data", "connectionLines", "mounted", "generateConnectionLines", "startCounterAnimation", "optimizeForMobile", "handleOrientationChange", "methods", "startTrial", "$router", "push", "contactUs", "console", "log", "getParticleStyle", "left", "Math", "random", "top", "animationDelay", "animationDuration", "getNodeStyle", "lines", "i", "id", "x1", "y1", "x2", "y2", "counters", "document", "querySelectorAll", "for<PERSON>ach", "counter", "target", "parseInt", "getAttribute", "increment", "current", "timer", "setInterval", "textContent", "clearInterval", "floor", "isMobile", "window", "innerWidth", "reduceMobileAnimations", "optimizeTouchEvents", "preloadCriticalImages", "particles", "particle", "index", "style", "display", "dataStreams", "stream", "touchElements", "element", "addEventListener", "transform", "passive", "setTimeout", "criticalImages", "src", "img", "Image"], "sources": ["src/views/About/AboutView.vue"], "sourcesContent": ["<template>\r\n  <Layout>\r\n    <!-- 顶部Banner -->\r\n    <div class=\"about-banner\">\r\n      <!-- 背景图层 -->\r\n      <div\r\n        class=\"about-banner-bg\"\r\n        style=\"background: url('images/earth.gif') center/cover; opacity: 0.7; z-index: 1; position: absolute; top: 0; left: 0; right: 0; bottom: 0;\"\r\n      ></div>\r\n\r\n      <!-- 动态网格背景 -->\r\n      <div class=\"grid-background\"></div>\r\n\r\n      <!-- 粒子效果 -->\r\n      <div class=\"particles-container\">\r\n        <div class=\"particle\" v-for=\"n in 50\" :key=\"n\" :style=\"getParticleStyle()\"></div>\r\n      </div>\r\n\r\n      <!-- 数据流效果 -->\r\n      <div class=\"data-streams\">\r\n        <div class=\"data-stream\" v-for=\"n in 8\" :key=\"n\"></div>\r\n      </div>\r\n\r\n      <!-- 算力节点连接线 -->\r\n      <!-- <div class=\"network-nodes\">\r\n        <div class=\"node\" v-for=\"n in 12\" :key=\"n\" :style=\"getNodeStyle()\">\r\n          <div class=\"node-pulse\"></div>\r\n        </div>\r\n        <svg class=\"connection-lines\" viewBox=\"0 0 100 100\">\r\n          <line v-for=\"line in connectionLines\" :key=\"line.id\"\r\n                :x1=\"line.x1\" :y1=\"line.y1\" :x2=\"line.x2\" :y2=\"line.y2\"\r\n                class=\"connection-line\"></line>\r\n        </svg>\r\n      </div> -->\r\n\r\n      <!-- 光效层 -->\r\n      <div class=\"light-effects\">\r\n        <div class=\"light-beam light-beam-1\"></div>\r\n        <div class=\"light-beam light-beam-2\"></div>\r\n        <div class=\"light-beam light-beam-3\"></div>\r\n      </div>\r\n\r\n      <div class=\"banner-content\">\r\n        <h1 class=\"banner-title\">\r\n          <span class=\"title-word\" data-text=\"承天工之智\">承天工之智</span>\r\n          <span class=\"title-separator\">，</span>\r\n          <span class=\"title-word\" data-text=\"启万物之能\">启万物之能</span>\r\n        </h1>\r\n        <div class=\"banner-subtitle-container\">\r\n          <p class=\"banner-subtitle typing-effect\">我们相信</p>\r\n          <p class=\"banner-subtitle typing-effect\" style=\"animation-delay: 1s;\">人类无需再围成一台机器</p>\r\n          <p class=\"banner-subtitle typing-effect\" style=\"animation-delay: 2s;\">而是用智能连接彼此，释放算力的真正价值</p>\r\n        </div>\r\n\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 关于我们 -->\r\n    <section class=\"about-section\">\r\n      <div class=\"container\">\r\n        <div class=\"am-g\">\r\n          <div class=\"am-u-md-6\">\r\n            <div class=\"our-company-text\">\r\n              <h1>关于我们</h1>\r\n              <p style=\"font-size: 17px;\">\r\n                天工开物智能科技（苏州）有限公司，致力于打造面向企业级用户的高性能计算解决方案，\r\n                围绕\"高效调度、低门槛使用、专业保障\"的核心理念，为 AI、大模型、图形渲染、科研计算等场景提供灵活、稳定、弹性的算力支持。\r\n              </p>\r\n              <p style=\"font-size: 17px;\">\r\n                我们基于全国分布式算力网络，自主构建智算调度平台，整合GPU资源与数据中心节点，\r\n                为企业提供从算力资源租用、模型部署优化到全流程运维服务的一站式专业方案。\r\n              </p>\r\n              <p style=\"font-size: 17px;\">\r\n                在智能时代的浪潮中，天工开物致力于打造企业级专业算力服务平台，\r\n                以全国分布式高性能计算网络为基础，提供稳定、高效、灵活可控的算力解决方案。\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <div class=\"am-u-md-6\">\r\n            <div class=\"our-company-quote\">\r\n              <div class=\"our-company-img\">\r\n                <img src=\"images/tgkw_about.jpg\" alt=\"天工开物智能科技\" loading=\"lazy\">\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n\r\n    <!-- 数据统计 -->\r\n    <!-- <section class=\"stats-section\">\r\n      <div class=\"container\">\r\n        <div class=\"am-g\">\r\n          <div class=\"am-u-sm-6 am-u-md-3\">\r\n            <div class=\"stat-item\">\r\n              <div class=\"stat-number\">32%</div>\r\n              <div class=\"stat-label\">市场占有率</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"am-u-sm-6 am-u-md-3\">\r\n            <div class=\"stat-item\">\r\n              <div class=\"stat-number\">27%</div>\r\n              <div class=\"stat-label\">年增长率</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"am-u-sm-6 am-u-md-3\">\r\n            <div class=\"stat-item\">\r\n              <div class=\"stat-number\">3000+</div>\r\n              <div class=\"stat-label\">服务客户</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"am-u-sm-6 am-u-md-3\">\r\n            <div class=\"stat-item\">\r\n              <div class=\"stat-number\">10000+</div>\r\n              <div class=\"stat-label\">GPU节点</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section> -->\r\n\r\n    <!-- 选择我们的理由 -->\r\n    <section class=\"our-mission\">\r\n      <div class=\"container\">\r\n        <div class=\"section--header\">\r\n          <h2 class=\"section--title\">选择我们的理由</h2>\r\n        </div>\r\n        <div class=\"am-g\">\r\n          <div class=\"am-u-sm-12 am-u-md-6 am-u-lg-3\">\r\n            <div class=\"our_mission--item\">\r\n              <div class=\"our_mission--item_media\">\r\n                <i class=\"am-icon-server\" style=\"font-size: 48px; color: #1470FF;\"></i>\r\n              </div>\r\n              <h4 class=\"our_mission--item_title\">企业级专业服务</h4>\r\n              <div class=\"our_mission--item_body\">\r\n                <p>为AI、科研、图形渲染、工业仿真等场景，提供稳定高效的高性能计算支持</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"am-u-sm-12 am-u-md-6 am-u-lg-3\">\r\n            <div class=\"our_mission--item\">\r\n              <div class=\"our_mission--item_media\">\r\n                <i class=\"am-icon-globe\" style=\"font-size: 48px; color: #1470FF;\"></i>\r\n              </div>\r\n              <h4 class=\"our_mission--item_title\">全国分布式节点布局</h4>\r\n              <div class=\"our_mission--item_body\">\r\n                <p>多地部署，动态调度，资源灵活，负载均衡，响应迅速</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"am-u-sm-12 am-u-md-6 am-u-lg-3\">\r\n            <div class=\"our_mission--item\">\r\n              <div class=\"our_mission--item_media\">\r\n                <i class=\"am-icon-cogs\" style=\"font-size: 48px; color: #1470FF;\"></i>\r\n              </div>\r\n              <h4 class=\"our_mission--item_title\">灵活弹性 + 高性价比</h4>\r\n              <div class=\"our_mission--item_body\">\r\n                <p>自研调度平台，支持定制，与大客户深度合作</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"am-u-sm-12 am-u-md-6 am-u-lg-3\">\r\n            <div class=\"our_mission--item\">\r\n              <div class=\"our_mission--item_media\">\r\n                <i class=\"am-icon-users\" style=\"font-size: 48px; color: #1470FF;\"></i>\r\n              </div>\r\n              <h4 class=\"our_mission--item_title\">更懂企业的算力伙伴</h4>\r\n              <div class=\"our_mission--item_body\">\r\n                <p>从需求对接、技术支持到运维保障，全流程一对一服务</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n\r\n    <!-- 核心团队 -->\r\n    <section class=\"our-team\">\r\n      <div class=\"container\">\r\n        <div class=\"section--header\">\r\n          <h2 class=\"section--title\">核心团队</h2>\r\n          <p class=\"section--description\">\r\n            核心团队来自知名AI云计算厂商、IDC运维专家与高校科研机构\r\n          </p>\r\n        </div>\r\n        <div class=\"am-g\">\r\n          <div class=\"am-u-sm-12 am-u-md-4\">\r\n            <div class=\"team-box\">\r\n              <div class=\"our-team-img\">\r\n                <img src=\"images/techteam.png\" alt=\"技术团队\" loading=\"lazy\">\r\n              </div>\r\n              <div class=\"team_member--body\">\r\n                <h4 class=\"team_member--name\">技术研发</h4>\r\n                <span class=\"team_member--position\">专业的研发团队，深耕AI算力调度与优化</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"am-u-sm-12 am-u-md-4\">\r\n            <div class=\"team-box\">\r\n              <div class=\"our-team-img\">\r\n                <img src=\"images/yunwei.png\" alt=\"运维团队\" loading=\"lazy\">\r\n              </div>\r\n              <div class=\"team_member--body\">\r\n                <h4 class=\"team_member--name\">运维保障</h4>\r\n                <span class=\"team_member--position\">5x8小时专业运维，确保服务稳定可靠</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"am-u-sm-12 am-u-md-4\">\r\n            <div class=\"team-box\">\r\n              <div class=\"our-team-img\">\r\n                <img src=\"images/khjl.png\" alt=\"客服团队\" loading=\"lazy\">\r\n              </div>\r\n              <div class=\"team_member--body\">\r\n                <h4 class=\"team_member--name\">客户服务</h4>\r\n                <span class=\"team_member--position\">专业客户经理，提供一对一贴心服务</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n\r\n    <!-- 联系我们 -->\r\n    <section class=\"contact-section\">\r\n      <div class=\"container\">\r\n        <div class=\"am-g\">\r\n          <div class=\"am-u-md-4\">\r\n            <div class=\"contact-item\">\r\n              <i class=\"am-icon-phone\"></i>\r\n              <h4>联系电话</h4>\r\n              <p>13913283376</p>\r\n            </div>\r\n          </div>\r\n          <div class=\"am-u-md-4\">\r\n            <div class=\"contact-item\">\r\n              <i class=\"am-icon-envelope\"></i>\r\n              <h4>官方公众号</h4>\r\n              <p>昆山新质创新数字技术研究院</p>\r\n            </div>\r\n          </div>\r\n          <div class=\"am-u-md-4\">\r\n            <div class=\"contact-item\">\r\n              <i class=\"am-icon-map-marker\"></i>\r\n              <h4>公司地址</h4>\r\n              <p>江苏省苏州市昆山市玉山镇祖冲之路1699号昆山工业技术研究院综合南楼1404</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n\r\n    <!-- 立即开始 -->\r\n    <section class=\"cta-section\">\r\n      <div class=\"container\">\r\n        <div class=\"cta-content\">\r\n          <h1 class=\"banner-text1\">连接智算未来，让高性能计算像水电一样可得、可控、可负担</h1>\r\n          <button class=\"cta-buttons\" @click=\"startTrial\">立即开始</button>\r\n        </div>\r\n      </div>\r\n      <!-- <div class=\"container\">\r\n        <div class=\"cta-content\">\r\n          <p>连接智算未来，让高性能计算像水电一样可得、可控、可负担</p>\r\n          <div class=\"cta-buttons\">\r\n            <button class=\"am-btn am-btn-primary am-btn-lg\" @click=\"startTrial\">立即开始</button>\r\n          </div>\r\n        </div>\r\n      </div> -->\r\n    </section>\r\n  </Layout>\r\n</template>\r\n\r\n<script>\r\nimport Layout from \"@/components/common/Layout\";\r\n\r\nexport default {\r\n  name: 'AboutView',\r\n  components: { Layout },\r\n  data() {\r\n    return {\r\n      connectionLines: []\r\n    }\r\n  },\r\n  mounted() {\r\n    this.generateConnectionLines();\r\n    this.startCounterAnimation();\r\n    this.optimizeForMobile();\r\n    this.handleOrientationChange();\r\n  },\r\n  methods: {\r\n    startTrial() {\r\n      this.$router.push('/product');\r\n    },\r\n    contactUs() {\r\n      // 可以添加联系我们的逻辑\r\n      console.log('联系我们');\r\n    },\r\n    getParticleStyle() {\r\n      return {\r\n        left: Math.random() * 100 + '%',\r\n        top: Math.random() * 100 + '%',\r\n        animationDelay: Math.random() * 10 + 's',\r\n        animationDuration: (Math.random() * 20 + 10) + 's'\r\n      };\r\n    },\r\n    getNodeStyle() {\r\n      return {\r\n        left: Math.random() * 90 + 5 + '%',\r\n        top: Math.random() * 90 + 5 + '%',\r\n        animationDelay: Math.random() * 3 + 's'\r\n      };\r\n    },\r\n    generateConnectionLines() {\r\n      const lines = [];\r\n      for (let i = 0; i < 15; i++) {\r\n        lines.push({\r\n          id: i,\r\n          x1: Math.random() * 100,\r\n          y1: Math.random() * 100,\r\n          x2: Math.random() * 100,\r\n          y2: Math.random() * 100\r\n        });\r\n      }\r\n      this.connectionLines = lines;\r\n    },\r\n    startCounterAnimation() {\r\n      const counters = document.querySelectorAll('.stat-number');\r\n      counters.forEach(counter => {\r\n        const target = parseInt(counter.getAttribute('data-target'));\r\n        const increment = target / 100;\r\n        let current = 0;\r\n\r\n        const timer = setInterval(() => {\r\n          current += increment;\r\n          if (current >= target) {\r\n            counter.textContent = target;\r\n            clearInterval(timer);\r\n          } else {\r\n            counter.textContent = Math.floor(current);\r\n          }\r\n        }, 50);\r\n      });\r\n    },\r\n\r\n    // 移动端优化\r\n    optimizeForMobile() {\r\n      // 检测是否为移动设备\r\n      const isMobile = window.innerWidth <= 768;\r\n\r\n      if (isMobile) {\r\n        // 减少动画复杂度\r\n        this.reduceMobileAnimations();\r\n\r\n        // 优化触摸事件\r\n        this.optimizeTouchEvents();\r\n\r\n        // 预加载关键图片\r\n        this.preloadCriticalImages();\r\n      }\r\n    },\r\n\r\n    reduceMobileAnimations() {\r\n      // 减少粒子数量\r\n      const particles = document.querySelectorAll('.particle');\r\n      particles.forEach((particle, index) => {\r\n        if (index > 20) { // 只保留前20个粒子\r\n          particle.style.display = 'none';\r\n        }\r\n      });\r\n\r\n      // 简化数据流动画\r\n      const dataStreams = document.querySelectorAll('.data-stream');\r\n      dataStreams.forEach((stream, index) => {\r\n        if (index > 4) { // 只保留前4个数据流\r\n          stream.style.display = 'none';\r\n        }\r\n      });\r\n    },\r\n\r\n    optimizeTouchEvents() {\r\n      // 为移动端优化触摸反馈\r\n      const touchElements = document.querySelectorAll('.our_mission--item, .team-box, .contact-item, .am-btn');\r\n\r\n      touchElements.forEach(element => {\r\n        element.addEventListener('touchstart', () => {\r\n          element.style.transform = 'translateY(-2px)';\r\n        }, { passive: true });\r\n\r\n        element.addEventListener('touchend', () => {\r\n          setTimeout(() => {\r\n            element.style.transform = '';\r\n          }, 150);\r\n        }, { passive: true });\r\n      });\r\n    },\r\n\r\n    preloadCriticalImages() {\r\n      const criticalImages = [\r\n        '/images/tiangonghead.jpeg',\r\n        '/images/back1.webp'\r\n      ];\r\n\r\n      criticalImages.forEach(src => {\r\n        const img = new Image();\r\n        img.src = src;\r\n      });\r\n    },\r\n\r\n    handleOrientationChange() {\r\n      // 处理屏幕方向变化\r\n      window.addEventListener('orientationchange', () => {\r\n        setTimeout(() => {\r\n          // 重新计算布局\r\n          this.generateConnectionLines();\r\n\r\n          // 重新优化移动端设置\r\n          this.optimizeForMobile();\r\n        }, 500);\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 顶部Banner */\r\n.about-banner {\r\n  background:\r\n    radial-gradient(ellipse at top, rgba(20, 112, 255, 0.3) 0%, transparent 70%),\r\n    radial-gradient(ellipse at bottom, rgba(74, 144, 255, 0.2) 0%, transparent 70%),\r\n    linear-gradient(135deg, #0a0a0a 0%, #1470FF 30%, #4A90FF 50%, #1470FF 70%, #0a0a0a 100%);\r\n  color: white;\r\n  padding: 260px 0 100px;\r\n  text-align: center;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.about-banner-bg {\r\n  pointer-events: none;\r\n}\r\n\r\n/* 动态网格背景 */\r\n.grid-background {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-image:\r\n    linear-gradient(rgba(20, 112, 255, 0.1) 1px, transparent 1px),\r\n    linear-gradient(90deg, rgba(20, 112, 255, 0.1) 1px, transparent 1px);\r\n  background-size: 50px 50px;\r\n  animation: gridMove 20s linear infinite;\r\n  z-index: 1;\r\n}\r\n\r\n@keyframes gridMove {\r\n  0% { transform: translate(0, 0); }\r\n  100% { transform: translate(50px, 50px); }\r\n}\r\n\r\n/* 粒子效果 */\r\n.particles-container {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  z-index: 2;\r\n}\r\n\r\n.particle {\r\n  position: absolute;\r\n  width: 2px;\r\n  height: 2px;\r\n  background: rgba(255, 255, 255, 0.8);\r\n  border-radius: 50%;\r\n  animation: particleFloat 15s linear infinite;\r\n}\r\n\r\n.particle:nth-child(3n) {\r\n  background: rgba(20, 112, 255, 0.8);\r\n  width: 3px;\r\n  height: 3px;\r\n}\r\n\r\n.particle:nth-child(5n) {\r\n  background: rgba(74, 144, 255, 0.6);\r\n  width: 1px;\r\n  height: 1px;\r\n}\r\n\r\n@keyframes particleFloat {\r\n  0% {\r\n    transform: translateY(100vh) translateX(0) scale(0);\r\n    opacity: 0;\r\n  }\r\n  10% {\r\n    opacity: 1;\r\n  }\r\n  90% {\r\n    opacity: 1;\r\n  }\r\n  100% {\r\n    transform: translateY(-100px) translateX(100px) scale(1);\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n\r\n/* 数据流效果 */\r\n.data-streams {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  z-index: 2;\r\n}\r\n\r\n.data-stream {\r\n  position: absolute;\r\n  width: 2px;\r\n  height: 100px;\r\n  background: linear-gradient(to bottom, transparent, rgba(20, 112, 255, 0.8), transparent);\r\n  animation: dataFlow 3s linear infinite;\r\n}\r\n\r\n.data-stream:nth-child(1) { left: 10%; animation-delay: 0s; }\r\n.data-stream:nth-child(2) { left: 25%; animation-delay: 0.5s; }\r\n.data-stream:nth-child(3) { left: 40%; animation-delay: 1s; }\r\n.data-stream:nth-child(4) { left: 55%; animation-delay: 1.5s; }\r\n.data-stream:nth-child(5) { left: 70%; animation-delay: 2s; }\r\n.data-stream:nth-child(6) { left: 85%; animation-delay: 2.5s; }\r\n.data-stream:nth-child(7) { left: 15%; animation-delay: 1.2s; }\r\n.data-stream:nth-child(8) { left: 75%; animation-delay: 0.8s; }\r\n\r\n@keyframes dataFlow {\r\n  0% {\r\n    transform: translateY(-100px);\r\n    opacity: 0;\r\n  }\r\n  50% {\r\n    opacity: 1;\r\n  }\r\n  100% {\r\n    transform: translateY(calc(100vh + 100px));\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n/* 算力节点连接线 */\r\n/* .network-nodes {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  z-index: 2;\r\n}\r\n\r\n.node {\r\n  position: absolute;\r\n  width: 8px;\r\n  height: 8px;\r\n  background: rgba(20, 112, 255, 0.8);\r\n  border-radius: 50%;\r\n  animation: nodePulse 2s ease-in-out infinite;\r\n}\r\n\r\n.node-pulse {\r\n  position: absolute;\r\n  top: -4px;\r\n  left: -4px;\r\n  width: 16px;\r\n  height: 16px;\r\n  border: 2px solid rgba(20, 112, 255, 0.4);\r\n  border-radius: 50%;\r\n  animation: pulseRing 2s ease-out infinite;\r\n}\r\n\r\n@keyframes nodePulse {\r\n  0%, 100% { transform: scale(1); opacity: 1; }\r\n  50% { transform: scale(1.2); opacity: 0.8; }\r\n}\r\n\r\n@keyframes pulseRing {\r\n  0% {\r\n    transform: scale(0.8);\r\n    opacity: 1;\r\n  }\r\n  100% {\r\n    transform: scale(2);\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n.connection-lines {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  pointer-events: none;\r\n}\r\n\r\n.connection-line {\r\n  stroke: rgba(20, 112, 255, 0.3);\r\n  stroke-width: 0.5;\r\n  animation: lineGlow 3s ease-in-out infinite alternate;\r\n}\r\n\r\n@keyframes lineGlow {\r\n  0% { stroke-opacity: 0.3; }\r\n  100% { stroke-opacity: 0.8; }\r\n} */\r\n\r\n/* 光效层 */\r\n.light-effects {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  z-index: 3;\r\n  pointer-events: none;\r\n}\r\n\r\n.light-beam {\r\n  position: absolute;\r\n  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);\r\n  animation: lightSweep 8s ease-in-out infinite;\r\n}\r\n\r\n.light-beam-1 {\r\n  top: 20%;\r\n  left: -100%;\r\n  width: 200%;\r\n  height: 2px;\r\n  animation-delay: 0s;\r\n}\r\n\r\n.light-beam-2 {\r\n  top: 60%;\r\n  left: -100%;\r\n  width: 200%;\r\n  height: 1px;\r\n  animation-delay: 2s;\r\n}\r\n\r\n.light-beam-3 {\r\n  top: 80%;\r\n  left: -100%;\r\n  width: 200%;\r\n  height: 3px;\r\n  animation-delay: 4s;\r\n}\r\n\r\n@keyframes lightSweep {\r\n  0% { transform: translateX(-100%); opacity: 0; }\r\n  50% { opacity: 1; }\r\n  100% { transform: translateX(100%); opacity: 0; }\r\n}\r\n\r\n.banner-content {\r\n  position: relative;\r\n  z-index: 10;\r\n}\r\n\r\n/* 标题效果 */\r\n.banner-title {\r\n  font-size: 48px;\r\n  margin-bottom: 30px;\r\n  font-weight: 700;\r\n  line-height: 1.2;\r\n  position: relative;\r\n}\r\n\r\n.title-word {\r\n  display: inline-block;\r\n  position: relative;\r\n  background: linear-gradient(45deg, #ffffff, #8fb9fd, #ffffff);\r\n  background-size: 200% 200%;\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n  animation: titleShimmer 3s ease-in-out infinite;\r\n}\r\n\r\n.title-word::before {\r\n  content: attr(data-text);\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  background: linear-gradient(45deg, transparent, rgba(255, 255, 255,1), transparent);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n  animation: textGlow 2s ease-in-out infinite alternate;\r\n}\r\n\r\n@keyframes titleShimmer {\r\n  0%, 100% { background-position: 0% 50%; }\r\n  50% { background-position: 100% 50%; }\r\n}\r\n\r\n@keyframes textGlow {\r\n  0% { opacity: 0; }\r\n  100% { opacity: 1; }\r\n}\r\n\r\n.title-separator {\r\n  color: rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n/* 字幕打字效果 */\r\n.banner-subtitle-container {\r\n  margin-bottom: 40px;\r\n}\r\n\r\n.banner-subtitle {\r\n  font-size: 24px;\r\n  margin-bottom: 10px;\r\n  opacity: 0;\r\n  transform: translateY(20px);\r\n  max-width: 600px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n}\r\n\r\n.typing-effect {\r\n  animation: typeIn 1s ease-out forwards;\r\n}\r\n\r\n@keyframes typeIn {\r\n  0% {\r\n    opacity: 0;\r\n    transform: translateY(20px);\r\n  }\r\n  100% {\r\n    opacity: 0.9;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n/* 关于我们部分 */\r\n.about-section {\r\n  padding: 80px 0;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #e6f2ff 100%);\r\n  position: relative;\r\n}\r\n\r\n/* 移动端文本优化 */\r\n.our-company-text h3 {\r\n  color: #333;\r\n  font-weight: 600;\r\n  margin-bottom: 25px;\r\n}\r\n\r\n.our-company-text p {\r\n  color: #555;\r\n  line-height: 1.7;\r\n  margin-bottom: 20px;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 图片优化 */\r\n.our-company-img {\r\n  text-align: center;\r\n  margin-top: 20px;\r\n}\r\n\r\n.our-company-img img {\r\n  max-width: 100%;\r\n  height: auto;\r\n  border-radius: 15px;\r\n  box-shadow: 0 8px 25px rgba(20, 112, 255, 0.15);\r\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n}\r\n\r\n.our-company-img img:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 15px 35px rgba(20, 112, 255, 0.25);\r\n}\r\n\r\n.about-section::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"dots\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\"><circle cx=\"10\" cy=\"10\" r=\"1\" fill=\"%23d4e8ff\" opacity=\"0.3\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23dots)\"/></svg>');\r\n  z-index: 1;\r\n}\r\n\r\n.about-section  {\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n/* .container {\r\n  width: 95%;\r\n  max-width: 1170px;\r\n  margin: 0 auto;\r\n  padding: 0 15px;\r\n} */\r\n\r\n/* 数据统计\r\n.stats-section {\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #e6f2ff 100%);\r\n  padding: 60px 0;\r\n  position: relative;\r\n}\r\n\r\n.stats-section::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grid\" width=\"10\" height=\"10\" patternUnits=\"userSpaceOnUse\"><path d=\"M 10 0 L 0 0 0 10\" fill=\"none\" stroke=\"%23d4e8ff\" stroke-width=\"0.5\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grid)\"/></svg>');\r\n  opacity: 0.3;\r\n  z-index: 1;\r\n}\r\n\r\n.stats-section .container {\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.stat-item {\r\n  text-align: center;\r\n  padding: 30px 15px;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.stat-item:hover {\r\n  transform: translateY(-5px);\r\n}\r\n\r\n.stat-number {\r\n  font-size: 48px;\r\n  font-weight: 700;\r\n  color: #1470FF;\r\n  margin-bottom: 10px;\r\n  text-shadow: 0 2px 4px rgba(20, 112, 255, 0.1);\r\n}\r\n\r\n.stat-label {\r\n  font-size: 16px;\r\n  color: #555;\r\n  font-weight: 500;\r\n} */\r\n\r\n/* 选择我们的理由 */\r\n.our-mission {\r\n  padding: 80px 0;\r\n  background: #fff;\r\n  position: relative;\r\n}\r\n\r\n.our-mission::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(45deg, transparent 49%, rgba(20, 112, 255, 0.02) 50%, transparent 51%);\r\n  z-index: 1;\r\n}\r\n\r\n.our-mission .container {\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.section--header {\r\n  text-align: center;\r\n  margin-bottom: 60px;\r\n}\r\n\r\n.section--title {\r\n  font-size: 36px;\r\n  color: #333;\r\n  margin-bottom: 20px;\r\n  font-weight: 600;\r\n  position: relative;\r\n}\r\n\r\n.section--title::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: -10px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 60px;\r\n  height: 3px;\r\n  background: linear-gradient(90deg, #1470FF, #4A90FF);\r\n  border-radius: 2px;\r\n}\r\n\r\n.section--description {\r\n  font-size: 18px;\r\n  color: #666;\r\n  max-width: 600px;\r\n  margin: 0 auto;\r\n  line-height: 1.6;\r\n}\r\n\r\n.our_mission--item {\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  transition: all 0.3s ease;\r\n  border-radius: 10px;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.our_mission--item::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(135deg, rgba(20, 112, 255, 0.05) 0%, rgba(74, 144, 255, 0.05) 100%);\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n  z-index: 1;\r\n}\r\n\r\n.our_mission--item:hover::before {\r\n  opacity: 1;\r\n}\r\n\r\n.our_mission--item:hover {\r\n  transform: translateY(-8px);\r\n  box-shadow: 0 15px 35px rgba(20, 112, 255, 0.15);\r\n}\r\n\r\n.our_mission--item_media {\r\n  margin-bottom: 25px;\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.our_mission--item_media i {\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.our_mission--item:hover .our_mission--item_media i {\r\n  color: #1470FF !important;\r\n  transform: scale(1.1);\r\n}\r\n\r\n.our_mission--item_title {\r\n  font-size: 20px;\r\n  color: #333;\r\n  margin-bottom: 15px;\r\n  font-weight: 600;\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.our_mission--item_body {\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.our_mission--item_body p {\r\n  font-size: 15px;\r\n  color: #666;\r\n  line-height: 1.6;\r\n  margin: 0;\r\n}\r\n\r\n/* 核心团队 */\r\n.our-team {\r\n  padding: 80px 0;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #e6f2ff 100%);\r\n  position: relative;\r\n}\r\n\r\n.our-team::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><circle cx=\"20\" cy=\"20\" r=\"2\" fill=\"%23d4e8ff\" opacity=\"0.5\"/><circle cx=\"80\" cy=\"20\" r=\"2\" fill=\"%23d4e8ff\" opacity=\"0.5\"/><circle cx=\"20\" cy=\"80\" r=\"2\" fill=\"%23d4e8ff\" opacity=\"0.5\"/><circle cx=\"80\" cy=\"80\" r=\"2\" fill=\"%23d4e8ff\" opacity=\"0.5\"/></svg>');\r\n  z-index: 1;\r\n}\r\n\r\n.our-team .container {\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.team-box {\r\n  background: #fff;\r\n  border-radius: 15px;\r\n  overflow: hidden;\r\n  box-shadow: 0 8px 25px rgba(20, 112, 255, 0.1);\r\n  transition: all 0.3s ease;\r\n  margin-bottom: 30px;\r\n  border: 1px solid rgba(20, 112, 255, 0.1);\r\n}\r\n\r\n.team-box:hover {\r\n  transform: translateY(-8px);\r\n  box-shadow: 0 20px 40px rgba(20, 112, 255, 0.2);\r\n  border-color: rgba(20, 112, 255, 0.3);\r\n}\r\n\r\n.our-team-img {\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.our-team-img::after {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(135deg, rgba(20, 112, 255, 0.1) 0%, rgba(74, 144, 255, 0.1) 100%);\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.team-box:hover .our-team-img::after {\r\n  opacity: 1;\r\n}\r\n\r\n.our-team-img img {\r\n  width: 100%;\r\n  height: 250px;\r\n  object-fit: cover;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.team-box:hover .our-team-img img {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.team_member--body {\r\n  padding: 30px 25px;\r\n  text-align: center;\r\n  position: relative;\r\n}\r\n\r\n.team_member--name {\r\n  font-size: 20px;\r\n  color: #333;\r\n  margin-bottom: 10px;\r\n  font-weight: 600;\r\n}\r\n\r\n.team_member--position {\r\n  font-size: 15px;\r\n  color: #666;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 联系我们 */\r\n.contact-section {\r\n  padding: 80px 0;\r\n  background: #fff;\r\n  position: relative;\r\n}\r\n\r\n.contact-section::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(45deg, transparent 49%, rgba(20, 112, 255, 0.02) 50%, transparent 51%);\r\n  z-index: 1;\r\n}\r\n\r\n.contact-section .container {\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.contact-item {\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  transition: all 0.3s ease;\r\n  border-radius: 10px;\r\n  position: relative;\r\n}\r\n\r\n.contact-item::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(135deg, rgba(20, 112, 255, 0.05) 0%, rgba(74, 144, 255, 0.05) 100%);\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n  border-radius: 10px;\r\n}\r\n\r\n.contact-item:hover::before {\r\n  opacity: 1;\r\n}\r\n\r\n.contact-item:hover {\r\n  transform: translateY(-5px);\r\n}\r\n\r\n.contact-item i {\r\n  font-size: 48px;\r\n  color: #1470FF;\r\n  margin-bottom: 20px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.contact-item:hover i {\r\n  color: #4A90FF;\r\n  transform: scale(1.1);\r\n}\r\n\r\n.contact-item h4 {\r\n  font-size: 20px;\r\n  color: #333;\r\n  margin-bottom: 15px;\r\n  font-weight: 600;\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.contact-item p {\r\n  font-size: 16px;\r\n  color: #666;\r\n  margin: 0;\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n/* 立即开始 */\r\n\r\n/* .cta-section {\r\n  background: linear-gradient(135deg, #0a0a0a 0%, #1470FF 30%, #4A90FF 50%, #1470FF 70%, #0a0a0a 100%);\r\n  width: 100%;\r\n  height: 120px;\r\n  color: white;\r\n  padding: 20px 0;\r\n  text-align: center;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.cta-section::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background:\r\n    radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 0%, transparent 50%),\r\n    radial-gradient(circle at 80% 20%, rgba(255,255,255,0.08) 0%, transparent 50%),\r\n    radial-gradient(circle at 40% 40%, rgba(255,255,255,0.06) 0%, transparent 50%),\r\n    linear-gradient(135deg, transparent 0%, rgba(255,255,255,0.03) 50%, transparent 100%);\r\n  animation: backgroundShift 8s ease-in-out infinite alternate;\r\n  z-index: 1;\r\n}\r\n\r\n@keyframes backgroundShift {\r\n  0% {\r\n    transform: translateX(0) translateY(0) scale(1) rotate(0deg);\r\n  }\r\n  20% {\r\n    transform: translateX(40px) translateY(-30px) scale(1.05) rotate(2deg);\r\n  }\r\n  40% {\r\n    transform: translateX(-35px) translateY(40px) scale(1.08) rotate(-1.5deg);\r\n  }\r\n  60% {\r\n    transform: translateX(25px) translateY(-25px) scale(1.04) rotate(1.2deg);\r\n  }\r\n  80% {\r\n    transform: translateX(-20px) translateY(30px) scale(1.06) rotate(-1deg);\r\n  }\r\n  100% {\r\n    transform: translateX(0) translateY(0) scale(1) rotate(0deg);\r\n  }\r\n}\r\n\r\n.cta-section::after {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background:\r\n    linear-gradient(90deg, rgba(10, 10, 10, 0.8) 0%, transparent 25%, transparent 75%, rgba(10, 10, 10, 0.8) 100%),\r\n    radial-gradient(circle at 30% 70%, rgba(20, 112, 255, 0.3) 0%, transparent 50%),\r\n    radial-gradient(circle at 70% 30%, rgba(74, 144, 255, 0.2) 0%, transparent 50%),\r\n    radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.05) 0%, transparent 70%);\r\n  z-index: 1;\r\n} */\r\n\r\n\r\n\r\n/* .cta-content {\r\n  position: relative;\r\n  z-index: 2;\r\n  animation: ctaGlow 3s ease-in-out infinite alternate;\r\n}\r\n\r\n@keyframes ctaGlow {\r\n  0% {\r\n    text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);\r\n  }\r\n  100% {\r\n    text-shadow: 0 0 30px rgba(255, 255, 255, 0.5), 0 0 40px rgba(20, 112, 255, 0.3);\r\n  }\r\n}\r\n\r\n.cta-content h2 {\r\n  font-size: 36px;\r\n  margin-bottom: 20px;\r\n  font-weight: 600;\r\n  text-shadow: 0 2px 4px rgba(0,0,0,0.3), 0 0 20px rgba(255, 255, 255, 0.2);\r\n  animation: titlePulse 2s ease-in-out infinite;\r\n}\r\n\r\n@keyframes titlePulse {\r\n  0%, 100% {\r\n    transform: scale(1);\r\n  }\r\n  50% {\r\n    transform: scale(1.02);\r\n  }\r\n}\r\n\r\n.cta-content p {\r\n  font-size: 18px;\r\n  margin-bottom: 40px;\r\n  opacity: 0.95;\r\n  max-width: 600px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n  text-shadow: 0 1px 2px rgba(0,0,0,0.2);\r\n} */\r\n\r\n/* .cta-buttons {\r\n  display: flex;\r\n  justify-content: right;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.cta-buttons .am-btn {\r\n  padding: 15px 35px;\r\n  font-size: 16px;\r\n  border-radius: 30px;\r\n  border: none;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  text-decoration: none;\r\n  display: inline-block;\r\n  font-weight: 500;\r\n  min-width: 160px;\r\n  box-shadow: 0 4px 15px rgba(0,0,0,0.2), 0 0 20px rgba(255, 255, 255, 0.1);\r\n  position: relative;\r\n  overflow: hidden;\r\n  animation: buttonPulse 2s ease-in-out infinite;\r\n}\r\n\r\n@keyframes buttonPulse {\r\n  0%, 100% {\r\n    box-shadow: 0 4px 15px rgba(0,0,0,0.2), 0 0 20px rgba(255, 255, 255, 0.1);\r\n  }\r\n  50% {\r\n    box-shadow: 0 6px 20px rgba(0,0,0,0.3), 0 0 30px rgba(255, 255, 255, 0.2);\r\n  }\r\n}\r\n\r\n.am-btn-primary {\r\n  background: linear-gradient(45deg, #fff 0%, #f0f8ff 100%);\r\n  color: #1470FF;\r\n  border: 2px solid #fff;\r\n}\r\n\r\n.am-btn-primary::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(20, 112, 255, 0.2), transparent);\r\n  transition: left 0.5s;\r\n}\r\n\r\n.am-btn-primary:hover::before {\r\n  left: 100%;\r\n}\r\n\r\n.am-btn-primary:hover {\r\n  background: linear-gradient(45deg, #f8f9fa 0%, #e6f2ff 100%);\r\n  transform: translateY(-5px) scale(1.05);\r\n  box-shadow: 0 10px 30px rgba(0,0,0,0.25), 0 0 40px rgba(20, 112, 255, 0.3);\r\n  color: #1470FF;\r\n  animation: none;\r\n}\r\n\r\n.am-btn-secondary:hover {\r\n  background: #fff;\r\n  color: #1470FF;\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 8px 25px rgba(0,0,0,0.15);\r\n}\r\n\r\n.am-btn:focus,\r\n.am-btn:active {\r\n  outline: none;\r\n  box-shadow: none;\r\n} */\r\n\r\n/* 响应式设计 */\r\n/* 平板设备 */\r\n@media (max-width: 1024px) {\r\n  .banner-title {\r\n    font-size: 40px;\r\n  }\r\n\r\n  .banner-subtitle {\r\n    font-size: 20px;\r\n  }\r\n\r\n  .about-banner {\r\n    padding: 100px 0 70px;\r\n  }\r\n\r\n  .section--title {\r\n    font-size: 32px;\r\n  }\r\n\r\n  .our_mission--item_media i {\r\n    font-size: 42px !important;\r\n  }\r\n\r\n  .contact-item i {\r\n    font-size: 42px;\r\n  }\r\n}\r\n\r\n/* 移动端 - 大屏手机 */\r\n@media (max-width: 768px) {\r\n  .container {\r\n    width: 100%;\r\n    padding: 0 20px;\r\n  }\r\n\r\n  .banner-title {\r\n    font-size: 32px;\r\n    line-height: 1.3;\r\n    margin-bottom: 25px;\r\n  }\r\n\r\n  .banner-subtitle {\r\n    font-size: 18px;\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .about-banner {\r\n    padding: 80px 0 60px;\r\n    min-height: 70vh;\r\n  }\r\n\r\n  .banner-subtitle-container {\r\n    margin-bottom: 30px;\r\n  }\r\n\r\n  /* 优化动画效果性能 */\r\n  .particle {\r\n    display: none; /* 移动端隐藏粒子效果以提升性能 */\r\n  }\r\n\r\n  .data-stream {\r\n    width: 1px;\r\n    height: 50px;\r\n  }\r\n\r\n  /* .node {\r\n    width: 6px;\r\n    height: 6px;\r\n  } */\r\n\r\n  /* .node-pulse {\r\n    width: 12px;\r\n    height: 12px;\r\n    top: -3px;\r\n    left: -3px;\r\n  } */\r\n\r\n  /* 减少光效以提升性能 */\r\n  .light-beam {\r\n    display: none;\r\n  }\r\n\r\n  /* 各个区块的移动端适配 */\r\n  .about-section,\r\n  .our-mission,\r\n  .our-team,\r\n  .contact-section {\r\n    padding: 50px 0;\r\n  }\r\n\r\n  .cta-section {\r\n    padding: 40px 0;\r\n  }\r\n\r\n  .section--title {\r\n    font-size: 28px;\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .section--description {\r\n    font-size: 16px;\r\n    padding: 0 10px;\r\n  }\r\n\r\n  /* 关于我们文本区域 */\r\n  .our-company-text {\r\n    margin-bottom: 30px;\r\n  }\r\n\r\n  .our-company-text h3 {\r\n    font-size: 24px;\r\n    margin-bottom: 20px;\r\n    text-align: center;\r\n  }\r\n\r\n  .our-company-text p {\r\n    font-size: 15px;\r\n    line-height: 1.6;\r\n    margin-bottom: 15px;\r\n    text-align: justify;\r\n  }\r\n\r\n  .our-company-img img {\r\n    max-width: 100%;\r\n    height: auto;\r\n    border-radius: 10px;\r\n  }\r\n\r\n  /* 选择我们的理由 */\r\n  .our_mission--item {\r\n    margin-bottom: 30px;\r\n    padding: 30px 15px;\r\n  }\r\n\r\n  .our_mission--item_media i {\r\n    font-size: 40px !important;\r\n  }\r\n\r\n  .our_mission--item_title {\r\n    font-size: 18px;\r\n    margin-bottom: 12px;\r\n  }\r\n\r\n  .our_mission--item_body p {\r\n    font-size: 14px;\r\n    line-height: 1.5;\r\n  }\r\n\r\n  /* 团队区域 */\r\n  .team-box {\r\n    margin-bottom: 25px;\r\n  }\r\n\r\n  .our-team-img img {\r\n    height: 200px;\r\n  }\r\n\r\n  .team_member--body {\r\n    padding: 20px 15px;\r\n  }\r\n\r\n  .team_member--name {\r\n    font-size: 18px;\r\n  }\r\n\r\n  .team_member--position {\r\n    font-size: 14px;\r\n  }\r\n\r\n  /* 联系我们 */\r\n  .contact-item {\r\n    padding: 30px 15px;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .contact-item i {\r\n    font-size: 40px;\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .contact-item h4 {\r\n    font-size: 18px;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .contact-item p {\r\n    font-size: 15px;\r\n  }\r\n\r\n  /* CTA区域 */\r\n  .cta-content h2 {\r\n    font-size: 28px;\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .cta-content p {\r\n    font-size: 16px;\r\n    margin-bottom: 30px;\r\n    padding: 0 10px;\r\n  }\r\n\r\n  .cta-buttons {\r\n    flex-direction: column;\r\n    align-items: center;\r\n    gap: 15px;\r\n  }\r\n\r\n  .cta-buttons .am-btn {\r\n    width: 220px;\r\n    padding: 12px 30px;\r\n    font-size: 15px;\r\n  }\r\n}\r\n\r\n/* 移动端 - 小屏手机 */\r\n@media (max-width: 480px) {\r\n  .container {\r\n    padding: 0 15px;\r\n  }\r\n\r\n  .banner-title {\r\n    font-size: 26px;\r\n    line-height: 1.2;\r\n  }\r\n\r\n  .banner-subtitle {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .about-banner {\r\n    padding: 60px 0 40px;\r\n    min-height: 60vh;\r\n  }\r\n\r\n  .section--title {\r\n    font-size: 24px;\r\n  }\r\n\r\n  .section--description {\r\n    font-size: 15px;\r\n  }\r\n\r\n  /* 关于我们文本 */\r\n  .our-company-text h3 {\r\n    font-size: 22px;\r\n  }\r\n\r\n  .our-company-text p {\r\n    font-size: 14px;\r\n    line-height: 1.5;\r\n  }\r\n\r\n  /* 选择我们的理由 */\r\n  .our_mission--item {\r\n    padding: 25px 10px;\r\n  }\r\n\r\n  .our_mission--item_media i {\r\n    font-size: 36px !important;\r\n  }\r\n\r\n  .our_mission--item_title {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .our_mission--item_body p {\r\n    font-size: 13px;\r\n  }\r\n\r\n  /* 团队区域 */\r\n  .our-team-img img {\r\n    height: 180px;\r\n  }\r\n\r\n  .team_member--body {\r\n    padding: 15px 10px;\r\n  }\r\n\r\n  .team_member--name {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .team_member--position {\r\n    font-size: 13px;\r\n  }\r\n\r\n  /* 联系我们 */\r\n  .contact-item {\r\n    padding: 25px 10px;\r\n  }\r\n\r\n  .contact-item i {\r\n    font-size: 36px;\r\n  }\r\n\r\n  .contact-item h4 {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .contact-item p {\r\n    font-size: 14px;\r\n  }\r\n\r\n  /* CTA区域 */\r\n  .cta-content h2 {\r\n    font-size: 24px;\r\n  }\r\n\r\n  .cta-content p {\r\n    font-size: 15px;\r\n    padding: 0 5px;\r\n  }\r\n\r\n  .cta-buttons .am-btn {\r\n    width: 200px;\r\n    padding: 10px 25px;\r\n    font-size: 14px;\r\n  }\r\n}\r\n\r\n/* 超小屏设备 */\r\n@media (max-width: 360px) {\r\n  .container {\r\n    padding: 0 10px;\r\n  }\r\n\r\n  .banner-title {\r\n    font-size: 22px;\r\n  }\r\n\r\n  .banner-subtitle {\r\n    font-size: 14px;\r\n  }\r\n\r\n  .section--title {\r\n    font-size: 20px;\r\n  }\r\n\r\n  .our_mission--item_media i {\r\n    font-size: 32px !important;\r\n  }\r\n\r\n  .contact-item i {\r\n    font-size: 32px;\r\n  }\r\n\r\n  .cta-buttons .am-btn {\r\n    width: 180px;\r\n    font-size: 13px;\r\n  }\r\n}\r\n\r\n/* 触摸设备优化 */\r\n@media (hover: none) and (pointer: coarse) {\r\n  .our_mission--item:hover,\r\n  .team-box:hover,\r\n  .contact-item:hover {\r\n    transform: none;\r\n  }\r\n\r\n  .our_mission--item:active,\r\n  .team-box:active,\r\n  .contact-item:active {\r\n    transform: translateY(-3px);\r\n    transition: transform 0.1s ease;\r\n  }\r\n\r\n  .cta-buttons .am-btn:hover {\r\n    transform: none;\r\n  }\r\n\r\n  .cta-buttons .am-btn:active {\r\n    transform: translateY(-2px) scale(0.98);\r\n    transition: transform 0.1s ease;\r\n  }\r\n}\r\n\r\n/* 横屏模式优化 */\r\n@media (max-width: 768px) and (orientation: landscape) {\r\n  .about-banner {\r\n    padding: 40px 0 30px;\r\n    min-height: 50vh;\r\n  }\r\n\r\n  .banner-title {\r\n    font-size: 28px;\r\n  }\r\n\r\n  .banner-subtitle {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .about-section,\r\n  .our-mission,\r\n  .our-team,\r\n  .contact-section {\r\n    padding: 40px 0;\r\n  }\r\n}\r\n\r\n/* 移动端性能优化 */\r\n@media (max-width: 768px) {\r\n  /* 启用硬件加速 */\r\n  .about-banner,\r\n  .our_mission--item,\r\n  .team-box,\r\n  .contact-item,\r\n  .am-btn {\r\n    -webkit-transform: translateZ(0);\r\n    transform: translateZ(0);\r\n    -webkit-backface-visibility: hidden;\r\n    backface-visibility: hidden;\r\n  }\r\n\r\n  /* 优化滚动性能 */\r\n  .about-banner {\r\n    -webkit-overflow-scrolling: touch;\r\n  }\r\n\r\n  /* 减少重绘 */\r\n  .particle,\r\n  .data-stream,\r\n  .node {\r\n    will-change: transform;\r\n  }\r\n\r\n  /* 触摸优化 */\r\n  .our_mission--item,\r\n  .team-box,\r\n  .contact-item,\r\n  .am-btn {\r\n    -webkit-tap-highlight-color: rgba(20, 112, 255, 0.1);\r\n  }\r\n\r\n  /* 防止文本选择 */\r\n  .banner-title,\r\n  .section--title,\r\n  .our_mission--item_title,\r\n  .team_member--name,\r\n  .contact-item h4 {\r\n    -webkit-user-select: none;\r\n    -moz-user-select: none;\r\n    -ms-user-select: none;\r\n    user-select: none;\r\n  }\r\n}\r\n\r\n/* 高分辨率屏幕优化 */\r\n@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {\r\n  .our-company-img img,\r\n  .our-team-img img {\r\n    image-rendering: -webkit-optimize-contrast;\r\n    image-rendering: crisp-edges;\r\n  }\r\n}\r\n\r\n/* 减少动画的偏好设置支持 */\r\n@media (prefers-reduced-motion: reduce) {\r\n  .particle,\r\n  .data-stream,\r\n  .node,\r\n  .light-beam,\r\n  .typing-effect,\r\n  .title-word {\r\n    animation: none !important;\r\n  }\r\n\r\n  .our_mission--item:hover,\r\n  .team-box:hover,\r\n  .contact-item:hover,\r\n  .am-btn:hover {\r\n    transform: none !important;\r\n    transition: none !important;\r\n  }\r\n}\r\n</style>"], "mappings": ";AAiRA,OAAAA,MAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAF;EAAA;EACAG,KAAA;IACA;MACAC,eAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,uBAAA;IACA,KAAAC,qBAAA;IACA,KAAAC,iBAAA;IACA,KAAAC,uBAAA;EACA;EACAC,OAAA;IACAC,WAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;IACA;IACAC,UAAA;MACA;MACAC,OAAA,CAAAC,GAAA;IACA;IACAC,iBAAA;MACA;QACAC,IAAA,EAAAC,IAAA,CAAAC,MAAA;QACAC,GAAA,EAAAF,IAAA,CAAAC,MAAA;QACAE,cAAA,EAAAH,IAAA,CAAAC,MAAA;QACAG,iBAAA,EAAAJ,IAAA,CAAAC,MAAA;MACA;IACA;IACAI,aAAA;MACA;QACAN,IAAA,EAAAC,IAAA,CAAAC,MAAA;QACAC,GAAA,EAAAF,IAAA,CAAAC,MAAA;QACAE,cAAA,EAAAH,IAAA,CAAAC,MAAA;MACA;IACA;IACAd,wBAAA;MACA,MAAAmB,KAAA;MACA,SAAAC,CAAA,MAAAA,CAAA,OAAAA,CAAA;QACAD,KAAA,CAAAZ,IAAA;UACAc,EAAA,EAAAD,CAAA;UACAE,EAAA,EAAAT,IAAA,CAAAC,MAAA;UACAS,EAAA,EAAAV,IAAA,CAAAC,MAAA;UACAU,EAAA,EAAAX,IAAA,CAAAC,MAAA;UACAW,EAAA,EAAAZ,IAAA,CAAAC,MAAA;QACA;MACA;MACA,KAAAhB,eAAA,GAAAqB,KAAA;IACA;IACAlB,sBAAA;MACA,MAAAyB,QAAA,GAAAC,QAAA,CAAAC,gBAAA;MACAF,QAAA,CAAAG,OAAA,CAAAC,OAAA;QACA,MAAAC,MAAA,GAAAC,QAAA,CAAAF,OAAA,CAAAG,YAAA;QACA,MAAAC,SAAA,GAAAH,MAAA;QACA,IAAAI,OAAA;QAEA,MAAAC,KAAA,GAAAC,WAAA;UACAF,OAAA,IAAAD,SAAA;UACA,IAAAC,OAAA,IAAAJ,MAAA;YACAD,OAAA,CAAAQ,WAAA,GAAAP,MAAA;YACAQ,aAAA,CAAAH,KAAA;UACA;YACAN,OAAA,CAAAQ,WAAA,GAAAzB,IAAA,CAAA2B,KAAA,CAAAL,OAAA;UACA;QACA;MACA;IACA;IAEA;IACAjC,kBAAA;MACA;MACA,MAAAuC,QAAA,GAAAC,MAAA,CAAAC,UAAA;MAEA,IAAAF,QAAA;QACA;QACA,KAAAG,sBAAA;;QAEA;QACA,KAAAC,mBAAA;;QAEA;QACA,KAAAC,qBAAA;MACA;IACA;IAEAF,uBAAA;MACA;MACA,MAAAG,SAAA,GAAApB,QAAA,CAAAC,gBAAA;MACAmB,SAAA,CAAAlB,OAAA,EAAAmB,QAAA,EAAAC,KAAA;QACA,IAAAA,KAAA;UAAA;UACAD,QAAA,CAAAE,KAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,MAAAC,WAAA,GAAAzB,QAAA,CAAAC,gBAAA;MACAwB,WAAA,CAAAvB,OAAA,EAAAwB,MAAA,EAAAJ,KAAA;QACA,IAAAA,KAAA;UAAA;UACAI,MAAA,CAAAH,KAAA,CAAAC,OAAA;QACA;MACA;IACA;IAEAN,oBAAA;MACA;MACA,MAAAS,aAAA,GAAA3B,QAAA,CAAAC,gBAAA;MAEA0B,aAAA,CAAAzB,OAAA,CAAA0B,OAAA;QACAA,OAAA,CAAAC,gBAAA;UACAD,OAAA,CAAAL,KAAA,CAAAO,SAAA;QACA;UAAAC,OAAA;QAAA;QAEAH,OAAA,CAAAC,gBAAA;UACAG,UAAA;YACAJ,OAAA,CAAAL,KAAA,CAAAO,SAAA;UACA;QACA;UAAAC,OAAA;QAAA;MACA;IACA;IAEAZ,sBAAA;MACA,MAAAc,cAAA,IACA,6BACA,qBACA;MAEAA,cAAA,CAAA/B,OAAA,CAAAgC,GAAA;QACA,MAAAC,GAAA,OAAAC,KAAA;QACAD,GAAA,CAAAD,GAAA,GAAAA,GAAA;MACA;IACA;IAEA1D,wBAAA;MACA;MACAuC,MAAA,CAAAc,gBAAA;QACAG,UAAA;UACA;UACA,KAAA3D,uBAAA;;UAEA;UACA,KAAAE,iBAAA;QACA;MACA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}