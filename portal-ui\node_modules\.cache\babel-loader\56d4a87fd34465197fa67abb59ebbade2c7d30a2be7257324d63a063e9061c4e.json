{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n// import Layout from \"@/components/common/Layout\";\nimport chatAi from \"@/components/common/mider/chatAi\";\nimport Mider from \"@/components/common/mider/Mider\";\nimport Footer from \"@/components/common/footer/Footer\";\nimport GpuComparison from \"@/views/Index/GpuComparison\";\nimport { postAnyData, getNotAuth, postNotAuth, getAnyData, postJsonData } from \"@/api/login\";\nimport { setToken } from \"@/utils/auth\";\nexport default {\n  name: \"IndexView\",\n  components: {\n    chatAi,\n    Footer,\n    Mider,\n    GpuComparison\n  },\n  computed: {\n    translate() {\n      return -this.translateX * 100 + \"%\";\n    },\n    isLogin() {\n      return !!document.cookie.includes('Admin-Token');\n    },\n    mobileTranslateX() {\n      return -this.mobileCurrentSlide * 100;\n    },\n    mobileApplications() {\n      return [this.firstRowWide, ...this.firstRowTallApps, ...this.secondRowApps, ...this.thirdRowSmallApps, this.thirdRowWide];\n    }\n  },\n  data() {\n    return {\n      touchStartX: 0,\n      touchEndX: 0,\n      touchThreshold: 50,\n      // 滑动阈值，单位px\n      isMobile: false,\n      mobileCurrentSlide: 0,\n      showContactModal: false,\n      contactInfo: {\n        name: '王先生',\n        phone: '13913283376'\n      },\n      tabIndex: 0,\n      firstRowWide: {\n        title: '工业制造',\n        image: require(\"../../assets/images/index/gongyezhizao.webp\"),\n        hover: false\n      },\n      firstRowTallApps: [{\n        title: '自动驾驶',\n        image: require(\"../../assets/images/index/zidongjiashi.webp\"),\n        hover: false\n      }, {\n        title: '智能交通',\n        image: require(\"../../assets/images/index/zhinengjiaotong.webp\"),\n        hover: false\n      }],\n      secondRowApps: [{\n        title: '智慧农业',\n        image: require(\"../../assets/images/index/zhihuinongye.webp\"),\n        hover: false\n      }, {\n        title: '影视渲染',\n        image: require(\"../../assets/images/index/yingshixuanran.webp\"),\n        hover: false\n      }],\n      thirdRowSmallApps: [{\n        title: '医疗影像',\n        image: require(\"../../assets/images/index/yiliaoyingxiang.webp\"),\n        hover: false\n      }, {\n        title: '金融风暴',\n        image: require(\"../../assets/images/index/jinrongfengbao.webp\"),\n        hover: false\n      }],\n      thirdRowWide: {\n        title: '能源科技',\n        image: require(\"../../assets/images/index/nengyuankeji.webp\"),\n        hover: false\n      },\n      bannerImages: [{\n        img: require(\"/public/images/back1.webp\"),\n        content: {\n          title: \"天工开物\",\n          text: \"构建AI应用周期服务的一站式算力云\",\n          position: \"left\",\n          thirdLink: \"/product\",\n          thirdBtnText: \"立即购买\"\n        }\n      }, {\n        img: require(\"/public/images/back2.webp\"),\n        content: {\n          title: \"专业AI训练平台\",\n          text: \"为AI+千行百业，提供高性能算力服务\",\n          position: \"left\",\n          secondaryLink: \"/login\",\n          secondaryBtnText: \"立即登录\",\n          primaryLink: '/register',\n          primaryBtnText: \"立即注册\"\n        }\n      }, {\n        img: require(\"/public/images/back3.webp\"),\n        content: {\n          title: \"企业级GPU集群\",\n          text: \"H100/H800/RTX 4090等高性能GPU随时可用，按需付费\",\n          position: \"left\"\n        }\n      }],\n      translateX: 0,\n      tsion: true,\n      tabList: [],\n      swiperOptions: {\n        loop: true,\n        autoplay: {\n          delay: 5000,\n          disableOnInteraction: false\n        },\n        pagination: {\n          el: '.swiper-pagination',\n          clickable: true\n        },\n        navigation: {\n          nextEl: '.swiper-button-next',\n          prevEl: '.swiper-button-prev'\n        }\n      },\n      serviceList: [{\n        id: 1,\n        icon: 'am-icon-shield',\n        title: '数据安全',\n        desc: '平台采取各种措施保证用户的数据安全，例如数据加密、防火墙、漏洞扫描和安全审计等措施，以防止数据泄露、篡改和丢失等风险。'\n      }, {\n        id: 2,\n        icon: 'am-icon-sliders',\n        title: '部署灵活',\n        desc: '租用GPU服务器具备比购买更高的灵活性，用户可以根据自己的需求随时调整租赁资源的配置及数量。'\n      }, {\n        id: 3,\n        icon: 'am-icon-server',\n        title: '高可靠性',\n        desc: '平台拥有完善的运维体系，采用丰富的数据备份、冗余技术以及定期检测等机制，保证租赁服务器的稳定性、易用性和安全性。'\n      }, {\n        id: 4,\n        icon: 'am-icon-rocket',\n        title: '高处理性能',\n        desc: '采用先进的GPU集群架构，平台能够大大提高计算速度和处理能力，可以在科学计算、深度学习等领域有更优秀的表现。'\n      }, {\n        id: 5,\n        icon: 'am-icon-credit-card',\n        title: '低成本',\n        desc: '购买GPU服务器需要投入较高的资金，而租赁可以让用户以较低的成本去获得相关基础设施，减轻了用户在预算上的负担。'\n      }, {\n        id: 6,\n        icon: 'am-icon-phone',\n        title: '及时服务',\n        desc: '提供365天 7*24小时技术支持及运维服务， 工程师现场及在线响应及电话支持。'\n      }],\n      comparisonGpus: [{\n        name: \"A100\",\n        architecture: \"Ampere\",\n        fp16Performance: \"312 TFLOPS\",\n        fp32Performance: \"19.5 TFLOPS\",\n        memory: \"80 GB\",\n        memoryType: \"HBM2\",\n        bandwidth: \"2,039 GB/s\"\n      }, {\n        name: \"A100\",\n        architecture: \"Ampere\",\n        fp16Performance: \"312 TFLOPS\",\n        fp32Performance: \"19.5 TFLOPS\",\n        memory: \"80 GB\",\n        memoryType: \"HBM2\",\n        bandwidth: \"2,039 GB/s\"\n      }, {\n        name: \"A100\",\n        architecture: \"Ampere\",\n        fp16Performance: \"312 TFLOPS\",\n        fp32Performance: \"19.5 TFLOPS\",\n        memory: \"80 GB\",\n        memoryType: \"HBM2\",\n        bandwidth: \"2,039 GB/s\"\n      }, {\n        name: \"V100\",\n        architecture: \"Volta\",\n        fp16Performance: \"125 TFLOPS\",\n        fp32Performance: \"15.7 TFLOPS\",\n        memory: \"32 GB\",\n        memoryType: \"HBM2\",\n        bandwidth: \"900 GB/s\"\n      }, {\n        name: \"A6000\",\n        architecture: \"Ampere\",\n        fp16Performance: \"77.4 TFLOPS\",\n        fp32Performance: \"38.7 TFLOPS\",\n        memory: \"48 GB\",\n        memoryType: \"GDDR6\",\n        bandwidth: \"768 GB/s\"\n      }, {\n        name: \"A5000\",\n        architecture: \"Ampere\",\n        fp16Performance: \"54.2 TFLOPS\",\n        fp32Performance: \"27.8 TFLOPS\",\n        memory: \"24 GB\",\n        memoryType: \"GDDR6\",\n        bandwidth: \"768 GB/s\"\n      }, {\n        name: \"A4000\",\n        architecture: \"Ampere\",\n        fp16Performance: \"19.17 TFLOPS\",\n        fp32Performance: \"19.17 TFLOPS\",\n        memory: \"16 GB\",\n        memoryType: \"GDDR6\",\n        bandwidth: \"448 GB/s\"\n      }],\n      gpus: [{\n        name: \"A100 SXM4 / 80GB\",\n        singlePrecision: \"156\",\n        halfPrecision: \"19.5\",\n        originalPrice: \"11.10\",\n        price: \"6.66\",\n        recommended: true,\n        isNew: false\n      }, {\n        name: \"RTX 3090 / 24GB\",\n        singlePrecision: \"35.58\",\n        halfPrecision: \"71.2\",\n        originalPrice: \"2.30\",\n        price: \"1.38\",\n        recommended: true,\n        isNew: false\n      }, {\n        name: \"A100 PCIE / 80GB\",\n        singlePrecision: \"156\",\n        halfPrecision: \"19.5\",\n        originalPrice: \"11.10\",\n        price: \"6.66\",\n        recommended: true,\n        isNew: false\n      }, {\n        name: \"RTX 4090 / 24GB\",\n        singlePrecision: \"82.58\",\n        halfPrecision: \"164.5\",\n        originalPrice: \"2.97\",\n        price: \"1.78\",\n        recommended: false,\n        isNew: true\n      }, {\n        name: \"RTX 4090D / 24GB\",\n        singlePrecision: \"73.54\",\n        halfPrecision: \"147.1\",\n        originalPrice: \"2.93\",\n        price: \"1.76\",\n        recommended: false,\n        isNew: false\n      }, {\n        name: \"RTX 3060 / 12GB\",\n        singlePrecision: \"12.7\",\n        halfPrecision: \"51.2\",\n        originalPrice: \"1.00\",\n        price: \"0.60\",\n        recommended: false,\n        isNew: false\n      }, {\n        name: \"RTX A4000 / 16GB\",\n        singlePrecision: \"19.17\",\n        halfPrecision: \"76.7\",\n        originalPrice: \"1.53\",\n        price: \"0.92\",\n        recommended: false,\n        isNew: false\n      }, {\n        name: \"Tesla P40 / 24GB\",\n        singlePrecision: \"5.9\",\n        halfPrecision: \"11.76\",\n        originalPrice: \"1.35\",\n        price: \"0.81\",\n        recommended: false,\n        isNew: false\n      }],\n      activeIndex: 0\n    };\n  },\n  created() {\n    this.fetchRecommendations();\n    const url = new URL(window.location.href);\n    const token = url.searchParams.get('token');\n    if (token) {\n      const hasRefreshed = localStorage.getItem('hasRefreshedWithToken');\n      // console.log('需要修改的参数为', url.origin + url.pathname)\n      if (!hasRefreshed) {\n        setToken(token);\n        localStorage.setItem('hasRefreshedWithToken', 'true');\n        // 直接修改 window.location（移除所有查询参数）\n        // console.log('需要修改的参数为', url.origin + url.pathname)\n      } else {\n        window.location.href = url.origin + url.pathname;\n        localStorage.setItem('hasRefreshedWithToken', 'false');\n      }\n    }\n  },\n  mounted() {\n    this.checkIsMobile();\n    window.addEventListener('resize', this.checkIsMobile);\n\n    // Banner auto-play\n    this.desktopInterval = setInterval(() => {\n      this.next();\n    }, 5000);\n    this.mobileInterval = setInterval(() => {\n      this.mobileNext();\n    }, 5000);\n  },\n  beforeDestroy() {\n    window.removeEventListener('resize', this.checkIsMobile);\n    clearInterval(this.desktopInterval);\n    clearInterval(this.mobileInterval);\n  },\n  methods: {\n    async fetchRecommendations() {\n      try {\n        getNotAuth(\"/system/recommend/list\").then(req => {\n          this.gpus = req.data.rows.map(item => ({\n            ...item,\n            isNew: item.isnew === 0,\n            recommended: item.recommended === 0\n          }));\n          // this.gpus = req.data.rows\n        });\n      } catch (error) {\n        console.error('获取GPU推荐列表失败:', error);\n      }\n    },\n    handleTouchStart(e) {\n      this.touchStartX = e.touches[0].clientX;\n    },\n    handleTouchMove(e) {\n      this.touchEndX = e.touches[0].clientX;\n    },\n    handleTouchEnd() {\n      if (!this.touchStartX || !this.touchEndX) return;\n      const diff = this.touchStartX - this.touchEndX;\n\n      // 向右滑动\n      if (diff > this.touchThreshold) {\n        this.mobileNext();\n      }\n\n      // 向左滑动\n      if (diff < -this.touchThreshold) {\n        this.mobilePrev();\n      }\n\n      // 重置触摸位置\n      this.touchStartX = 0;\n      this.touchEndX = 0;\n    },\n    mobilePrev() {\n      this.mobileCurrentSlide = (this.mobileCurrentSlide - 1 + this.bannerImages.length) % this.bannerImages.length;\n      this.resetMobileInterval();\n    },\n    mobileNext() {\n      this.mobileCurrentSlide = (this.mobileCurrentSlide + 1) % this.bannerImages.length;\n      this.resetMobileInterval();\n    },\n    resetMobileInterval() {\n      clearInterval(this.mobileInterval);\n      this.mobileInterval = setInterval(() => {\n        this.mobileNext();\n      }, 5000);\n    },\n    checkIsMobile() {\n      this.isMobile = window.innerWidth <= 768;\n    },\n    openContactModal() {\n      this.showContactModal = true;\n    },\n    closeContactModal() {\n      this.showContactModal = false;\n    },\n    handleBannerAction(item) {\n      if (item.content.link) {\n        this.$router.push(item.content.link);\n      }\n      this.$ga.event('Banner', 'click', item.content.title);\n    },\n    navigateTo(path) {\n      if (this.currentPath && this.currentPath !== path) {\n        this.previousActivePath = this.currentPath;\n        this.$nextTick(() => {\n          const navLinks = document.querySelectorAll('.nav-link, .btn-login');\n          navLinks.forEach(link => {\n            if ((link.classList.contains('active') || path === '/login' && link.classList.contains('btn-login')) && !link.classList.contains('active-exit')) {\n              link.classList.add('active-exit');\n              setTimeout(() => {\n                link.classList.remove('active-exit');\n              }, 300);\n            }\n          });\n          this.currentPath = path;\n        });\n      } else {\n        this.currentPath = path;\n      }\n      if (this.$route.path === path) {\n        this.$nextTick(() => {\n          window.scrollTo({\n            top: 0,\n            behavior: 'instant'\n          });\n          this.$router.go(0);\n        });\n      } else {\n        this.$router.push(path);\n        window.scrollTo({\n          top: 0,\n          behavior: 'instant'\n        });\n      }\n    },\n    last() {\n      this.translateX = (this.translateX - 1 + this.bannerImages.length) % this.bannerImages.length;\n      this.tsion = true;\n    },\n    next() {\n      this.translateX = (this.translateX + 1) % this.bannerImages.length;\n      this.tsion = true;\n    },\n    goToSlide(index) {\n      this.mobileCurrentSlide = index;\n      // 重置自动播放计时器\n      clearInterval(this.mobileInterval);\n      this.mobileInterval = setInterval(() => {\n        this.mobileNext();\n      }, 5000);\n    },\n    changeTab(index) {\n      this.tabIndex = index;\n    },\n    useGpu(gpu) {\n      console.log(`Selected GPU: ${gpu.name}`);\n    },\n    getCustomSolution() {\n      console.log('Get Custom Solution');\n    },\n    contactUs() {\n      console.log('Contact Us');\n    },\n    prevSlide() {\n      this.activeIndex = this.activeIndex > 0 ? this.activeIndex - 1 : this.slideshow.length - 1;\n    },\n    nextSlide() {\n      this.activeIndex = this.activeIndex < this.slideshow.length - 1 ? this.activeIndex + 1 : 0;\n    },\n    selectGpu(gpu) {\n      console.log(`Selected GPU: ${gpu.name}`);\n    }\n  },\n  watch: {\n    translateX(newVal) {\n      const dots = this.$refs.swiperPagination?.querySelectorAll(\"span\");\n      if (dots) {\n        dots.forEach((dot, index) => {\n          dot.classList.toggle(\"active\", index === newVal);\n        });\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["chatAi", "<PERSON><PERSON>", "Footer", "GpuComparison", "postAnyData", "getNotAuth", "postNotAuth", "getAnyData", "postJsonData", "setToken", "name", "components", "computed", "translate", "translateX", "is<PERSON>ogin", "document", "cookie", "includes", "mobileTranslateX", "mobileCurrentSlide", "mobileApplications", "firstRowWide", "firstRowTallApps", "secondRowApps", "thirdRowSmallApps", "thirdRowWide", "data", "touchStartX", "touchEndX", "touchThreshold", "isMobile", "showContactModal", "contactInfo", "phone", "tabIndex", "title", "image", "require", "hover", "bannerImages", "img", "content", "text", "position", "thirdLink", "thirdBtnText", "secondaryLink", "secondaryBtnText", "primaryLink", "primaryBtnText", "tsion", "tabList", "swiperOptions", "loop", "autoplay", "delay", "disableOnInteraction", "pagination", "el", "clickable", "navigation", "nextEl", "prevEl", "serviceList", "id", "icon", "desc", "comparisonGpus", "architecture", "fp16Performance", "fp32Performance", "memory", "memoryType", "bandwidth", "gpus", "singlePrecision", "halfPrecision", "originalPrice", "price", "recommended", "isNew", "activeIndex", "created", "fetchRecommendations", "url", "URL", "window", "location", "href", "token", "searchParams", "get", "hasRefreshed", "localStorage", "getItem", "setItem", "origin", "pathname", "mounted", "checkIsMobile", "addEventListener", "desktopInterval", "setInterval", "next", "mobileInterval", "mobileNext", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "clearInterval", "methods", "then", "req", "rows", "map", "item", "isnew", "error", "console", "handleTouchStart", "e", "touches", "clientX", "handleTouchMove", "handleTouchEnd", "diff", "mobilePrev", "length", "resetMobileInterval", "innerWidth", "openContactModal", "closeContactModal", "handleBannerAction", "link", "$router", "push", "$ga", "event", "navigateTo", "path", "currentPath", "previousActivePath", "$nextTick", "navLinks", "querySelectorAll", "for<PERSON>ach", "classList", "contains", "add", "setTimeout", "remove", "$route", "scrollTo", "top", "behavior", "go", "last", "goToSlide", "index", "changeTab", "useGpu", "gpu", "log", "getCustomSolution", "contactUs", "prevSlide", "slideshow", "nextSlide", "selectGpu", "watch", "newVal", "dots", "$refs", "swiperPagination", "dot", "toggle"], "sources": ["src/views/Index/IndexView.vue"], "sourcesContent": ["<template>\r\n\r\n  <div>\r\n\r\n    <!-- 电脑端布局 -->\r\n    <div v-if=\"!isMobile\" class=\"desktop-layout\">\r\n      <!-- Banner Section -->\r\n      <div class=\"banner-section\">\r\n        <div class=\"banner-container\">\r\n          <div class=\"big-box\">\r\n            <div class=\"img-box\">\r\n              <div class=\"show-box\"\r\n                   :style=\"{\r\n                    transform: 'translateX(' + translate + ')',\r\n                    transition: tsion ? 'all 0.5s' : 'none',\r\n                  }\">\r\n                <div class=\"slide-item\" v-for=\"(item, index) in bannerImages\" :key=\"index\">\r\n                  <img :src=\"item.img\" alt=\"\" />\r\n                  <div class=\"banner-content\" :class=\"'pos-' + item.content.position\">\r\n                    <h2 class=\"banner-title\" v-html=\"item.content.title\"></h2>\r\n                    <p class=\"banner-text\">{{ item.content.text }}</p>\r\n                    <div class=\"banner-actions\">\r\n                      <a v-if=\"!isLogin && item.content.secondaryLink\"\r\n                         href=\"#\"\r\n                         @click.prevent=\"navigateTo(item.content.secondaryLink)\"\r\n                         class=\"banner-button secondary-btn\">\r\n                        {{ item.content.secondaryBtnText }}\r\n                      </a>\r\n                      <a v-if=\"!isLogin && item.content.primaryLink\"\r\n                         href=\"#\"\r\n                         @click.prevent=\"navigateTo(item.content.primaryLink)\"\r\n                         class=\"banner-button primary-btn\">\r\n                        {{ item.content.primaryBtnText }}\r\n                      </a>\r\n                      <a v-if=\"item.content.thirdLink\"\r\n                         href=\"#\"\r\n                         @click.prevent=\"navigateTo(item.content.thirdLink)\"\r\n                         class=\"banner-button secondary-btn\">\r\n                        {{ item.content.thirdBtnText }}\r\n                      </a>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"arrowhead-box\">\r\n              <span @click=\"last\" class=\"nav-arrow left\">\r\n                <img src=\"../../assets/images/index/right-arrow.png\" alt=\"\" class=\"arrow-icon rotated\">\r\n              </span>\r\n              <span @click=\"next\" class=\"nav-arrow right\">\r\n                <img src=\"../../assets/images/index/right-arrow.png\" alt=\"\" class=\"arrow-icon\">\r\n              </span>\r\n            </div>\r\n            <div class=\"swiper-pagination\" ref=\"swiperPagination\">\r\n              <span v-for=\"(item, index) in bannerImages\" :key=\"index\" :class=\"{ active: translateX === index }\"></span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 为您推荐 Section  -->\r\n      <section class=\"section gpu-section\">\r\n        <div class=\"container\">\r\n          <div class=\"section-header\">\r\n            <h2 class=\"section-title\">为您推荐</h2>\r\n            <p class=\"section-description\">\r\n              专注于提供高性能、稳定可靠的 GPU 算力服务，兼具灵活配置与优质性价比。\r\n            </p>\r\n          </div>\r\n\r\n          <div class=\"gpu-card-grid\">\r\n            <div\r\n                v-for=\"(gpu, index) in gpus\"\r\n                :key=\"index\"\r\n                class=\"gpu-card\"\r\n                :class=\"{ 'recommended': gpu.recommended }\"\r\n                @click=\"navigateTo('/product')\"\r\n            >\r\n              <div class=\"gpu-card-header\">\r\n                <h3 class=\"gpu-name\">{{ gpu.name }}</h3>\r\n                <span v-if=\"gpu.recommended\" class=\"recommendation-tag\">推荐</span>\r\n                <span v-if=\"gpu.isNew\" class=\"new-tag\">NEW</span>\r\n              </div>\r\n\r\n              <div class=\"gpu-specs-pricing\">\r\n                <div class=\"specs-section\">\r\n                  <div class=\"spec-item\">\r\n                    <span class=\"spec-label\">单精度:</span>\r\n                    <span class=\"spec-value\">{{ gpu.singlePrecision }} TFLOPS</span>\r\n                  </div>\r\n                  <div class=\"spec-item\">\r\n                    <span class=\"spec-label\">半精度:</span>\r\n                    <span class=\"spec-value\">{{ gpu.halfPrecision }} Tensor TFL</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"price-section\">\r\n                  <div class=\"gpu-pricing\">\r\n                    <span class=\"original-price\" v-if=\"gpu.originalPrice\">¥{{ gpu.originalPrice }}/时</span>\r\n                    <span class=\"current-price\">¥<span class=\"price-value\">{{ gpu.price }}</span>/时</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      <GpuComparison></GpuComparison>\r\n\r\n      <!-- 核心优势 Section -->\r\n      <section class=\"section services-section\">\r\n        <div class=\"container\">\r\n          <div class=\"section-header\">\r\n            <h2 class=\"section-title\">核心优势</h2>\r\n            <p class=\"section-description\">\r\n              专业铸造优秀,天工开物企业AI变革路上的好伙伴。\r\n            </p>\r\n          </div>\r\n\r\n          <div class=\"services-grid\">\r\n            <div class=\"service-item\" v-for=\"(service,index) in serviceList\" :key=\"index\">\r\n              <div class=\"service-card\">\r\n                <i class=\"service-icon\" :class=\"service.icon\"></i>\r\n                <h3 class=\"service-title\">{{ service.title }}</h3>\r\n                <div class=\"service-text\"><p>{{service.desc}}</p></div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      <!-- Application Areas Section -->\r\n      <section class=\"appsec-section\">\r\n        <div class=\"section-header\">\r\n          <h2 class=\"section-title\">行业应用</h2>\r\n          <p class=\"section-description\">\r\n            Applications\r\n          </p>\r\n        </div>\r\n\r\n        <div class=\"appsec-container\">\r\n          <div class=\"appsec-grid\">\r\n            <!-- 第一行 -->\r\n            <!-- 宽幅项目 (2x1) -->\r\n            <div class=\"appsec-item appsec-wide\">\r\n              <div class=\"appsec-card\" @mouseover=\"firstRowWide.hover = true\" @mouseleave=\"firstRowWide.hover = false\">\r\n                <div class=\"appsec-image\" :class=\"{ 'appsec-hover': firstRowWide.hover }\">\r\n                  <img :src=\"firstRowWide.image\" :alt=\"firstRowWide.title\">\r\n                </div>\r\n                <div class=\"appsec-cardtitle\" :class=\"{ 'appsec-hover': firstRowWide.hover }\">{{ firstRowWide.title }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 两个高竖幅项目 (1x2) -->\r\n            <div class=\"appsec-item appsec-tall\" v-for=\"(app, index) in firstRowTallApps\" :key=\"'tall-'+index\">\r\n              <div class=\"appsec-card\" @mouseover=\"app.hover = true\" @mouseleave=\"app.hover = false\">\r\n                <div class=\"appsec-image\" :class=\"{ 'appsec-hover': app.hover }\">\r\n                  <img :src=\"app.image\" :alt=\"app.title\">\r\n                </div>\r\n                <div class=\"appsec-cardtitle\" :class=\"{ 'appsec-hover': app.hover }\">{{ app.title }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 第二行 -->\r\n            <!-- 两个小方形 (1x1) 放在第一行宽幅图片下方 -->\r\n            <div class=\"appsec-item appsec-small\" v-for=\"(app, index) in secondRowApps\" :key=\"'small-'+index\">\r\n              <div class=\"appsec-card\" @mouseover=\"app.hover = true\" @mouseleave=\"app.hover = false\">\r\n                <div class=\"appsec-image\" :class=\"{ 'appsec-hover': app.hover }\">\r\n                  <img :src=\"app.image\" :alt=\"app.title\">\r\n                </div>\r\n                <div class=\"appsec-cardtitle\" :class=\"{ 'appsec-hover': app.hover }\">{{ app.title }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 第三行 -->\r\n            <!-- 两个小方形 (1x1) -->\r\n            <div class=\"appsec-item appsec-small\" v-for=\"(app, index) in thirdRowSmallApps\" :key=\"'third-small-'+index\">\r\n              <div class=\"appsec-card\" @mouseover=\"app.hover = true\" @mouseleave=\"app.hover = false\">\r\n                <div class=\"appsec-image\" :class=\"{ 'appsec-hover': app.hover }\">\r\n                  <img :src=\"app.image\" :alt=\"app.title\">\r\n                </div>\r\n                <div class=\"appsec-cardtitle\" :class=\"{ 'appsec-hover': app.hover }\">{{ app.title }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 一个宽幅项目 (2x1) -->\r\n            <div class=\"appsec-item appsec-wide\">\r\n              <div class=\"appsec-card\" @mouseover=\"thirdRowWide.hover = true\" @mouseleave=\"thirdRowWide.hover = false\">\r\n                <div class=\"appsec-image\" :class=\"{ 'appsec-hover': thirdRowWide.hover }\">\r\n                  <img :src=\"thirdRowWide.image\" :alt=\"thirdRowWide.title\">\r\n                </div>\r\n                <div class=\"appsec-cardtitle\" :class=\"{ 'appsec-hover': thirdRowWide.hover }\">{{ thirdRowWide.title }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <chat-ai/>\r\n        </div>\r\n      </section>\r\n\r\n      <div class=\"recommendation-tag1\">\r\n        <div class=\"card1\">\r\n          <h1 class=\"banner-text1\">为AI+千行百业，提供高性能算力服务</h1>\r\n          <button class=\"consult-button1\" @click=\"openContactModal\"></button>\r\n        </div>\r\n      </div>\r\n\r\n      <transition name=\"fade\">\r\n        <div v-if=\"showContactModal\" class=\"contact-modal-overlay\" @click.self=\"closeContactModal\">\r\n          <div class=\"contact-modal\">\r\n            <button class=\"close-modal\" @click=\"closeContactModal\">\r\n              &times;\r\n            </button>\r\n            <div class=\"contact-content\">\r\n              <div class=\"contact-item\">\r\n                <i class=\"am-icon-user\"></i>\r\n                <span>{{ contactInfo.name }}</span>\r\n              </div>\r\n              <div class=\"contact-item\">\r\n                <i class=\"am-icon-phone\"></i>\r\n                <span>{{ contactInfo.phone }}</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"contact-note\">\r\n              <p>欢迎随时来电咨询</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </transition>\r\n    </div>\r\n\r\n    <!-- 移动端布局 -->\r\n    <div v-else class=\"mobile-layout\">\r\n\r\n      <div class=\"mobile-banner\"\r\n           @touchstart=\"handleTouchStart\"\r\n           @touchmove=\"handleTouchMove\"\r\n           @touchend=\"handleTouchEnd\">\r\n        <div class=\"mobile-banner-slider\" :style=\"{ transform: `translateX(${-mobileCurrentSlide * 100}%)` }\">\r\n          <div class=\"mobile-slide\" v-for=\"(item, index) in bannerImages\" :key=\"index\">\r\n            <div class=\"mobile-slide-inner\">\r\n              <img :src=\"item.img\" alt=\"\" class=\"mobile-slide-img\">\r\n              <div class=\"mobile-banner-content\" :class=\"'pos-' + item.content.position\">\r\n                <h2 class=\"mobile-banner-title\" v-html=\"item.content.title\"></h2>\r\n                <p class=\"mobile-banner-text\">{{ item.content.text }}</p>\r\n                <div class=\"mobile-banner-actions\">\r\n                  <a v-if=\"!isLogin && item.content.secondaryLink\"\r\n                     href=\"#\"\r\n                     @click.prevent=\"navigateTo(item.content.secondaryLink)\"\r\n                     class=\"mobile-banner-button secondary-btn\">\r\n                    {{ item.content.secondaryBtnText }}\r\n                  </a>\r\n                  <a v-if=\"!isLogin && item.content.primaryLink\"\r\n                     href=\"#\"\r\n                     @click.prevent=\"navigateTo(item.content.primaryLink)\"\r\n                     class=\"mobile-banner-button primary-btn\">\r\n                    {{ item.content.primaryBtnText }}\r\n                  </a>\r\n                  <a v-if=\"item.content.thirdLink\"\r\n                     href=\"#\"\r\n                     @click.prevent=\"navigateTo(item.content.thirdLink)\"\r\n                     class=\"banner-button secondary-btn\">\r\n                    {{ item.content.thirdBtnText }}\r\n                  </a>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"mobile-banner-pagination\">\r\n    <span v-for=\"(item, index) in bannerImages\"\r\n          :key=\"index\"\r\n          :class=\"{ active: mobileCurrentSlide === index }\"\r\n          @click=\"goToSlide(index)\"></span>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 移动端 GPU 推荐 -->\r\n      <section class=\"mobile-section mobile-gpu-section\">\r\n        <div class=\"mobile-section-header\">\r\n          <h2 class=\"mobile-section-title\">为您推荐</h2>\r\n          <p class=\"mobile-section-description\">\r\n            专注于提供高性能、稳定可靠的 GPU 算力服务\r\n          </p>\r\n        </div>\r\n\r\n        <div class=\"mobile-gpu-list\">\r\n          <div class=\"mobile-gpu-card\"\r\n               v-for=\"(gpu, index) in gpus\"\r\n               :key=\"index\"\r\n               :class=\"{ 'recommended': gpu.recommended }\"\r\n               @click=\"navigateTo('/product')\">\r\n            <div class=\"mobile-gpu-header\">\r\n              <h3 class=\"mobile-gpu-name\">{{ gpu.name }}</h3>\r\n              <div class=\"mobile-gpu-tags\">\r\n                <span v-if=\"gpu.recommended\" class=\"mobile-recommend-tag\">推荐</span>\r\n                <span v-if=\"gpu.isNew\" class=\"mobile-new-tag\">NEW</span>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"mobile-gpu-specs\">\r\n              <div class=\"mobile-spec-item\">\r\n                <span class=\"mobile-spec-label\">单精度:</span>\r\n                <span class=\"mobile-spec-value\">{{ gpu.singlePrecision }} TFLOPS</span>\r\n              </div>\r\n              <div class=\"mobile-spec-item\">\r\n                <span class=\"mobile-spec-label\">半精度:</span>\r\n                <span class=\"mobile-spec-value\">{{ gpu.halfPrecision }} Tensor TFL</span>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"mobile-gpu-price\">\r\n              <span class=\"mobile-original-price\" v-if=\"gpu.originalPrice\">¥{{ gpu.originalPrice }}/时</span>\r\n              <span class=\"mobile-current-price\">¥{{ gpu.price }}/时</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      <!-- 移动端 GPU 对比 -->\r\n      <section class=\"mobile-section mobile-comparison-section\">\r\n        <div class=\"mobile-section-header\">\r\n          <h2 class=\"mobile-section-title\">GPU性能对比</h2>\r\n          <p class=\"mobile-section-description\">\r\n            专业GPU性能详细对比\r\n          </p>\r\n        </div>\r\n\r\n        <div class=\"mobile-comparison-container\">\r\n          <div class=\"mobile-comparison-scroll\">\r\n            <table class=\"mobile-comparison-table\">\r\n              <thead>\r\n              <tr>\r\n                <th>GPU型号</th>\r\n                <th v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.name }}</th>\r\n              </tr>\r\n              </thead>\r\n              <tbody>\r\n              <tr>\r\n                <td>架构</td>\r\n                <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.architecture }}</td>\r\n              </tr>\r\n              <tr>\r\n                <td>FP16性能</td>\r\n                <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.fp16Performance }}</td>\r\n              </tr>\r\n              <tr>\r\n                <td>FP32性能</td>\r\n                <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.fp32Performance }}</td>\r\n              </tr>\r\n              <tr>\r\n                <td>显存</td>\r\n                <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.memory }}</td>\r\n              </tr>\r\n              <tr>\r\n                <td>显存类型</td>\r\n                <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.memoryType }}</td>\r\n              </tr>\r\n              <tr>\r\n                <td>带宽</td>\r\n                <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.bandwidth }}</td>\r\n              </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      <!-- 移动端 核心优势 -->\r\n      <section class=\"mobile-section mobile-services-section\">\r\n        <div class=\"mobile-section-header\">\r\n          <h2 class=\"mobile-section-title\">核心优势</h2>\r\n          <p class=\"mobile-section-description\">\r\n            专业铸造优秀,天工开物企业AI变革路上的好伙伴\r\n          </p>\r\n        </div>\r\n\r\n        <div class=\"mobile-services-list\">\r\n          <div class=\"mobile-service-card\" v-for=\"(service, index) in serviceList\" :key=\"index\">\r\n            <div class=\"mobile-service-icon\">\r\n              <i :class=\"service.icon\"></i>\r\n            </div>\r\n            <h3 class=\"mobile-service-title\">{{ service.title }}</h3>\r\n            <p class=\"mobile-service-desc\">{{ service.desc }}</p>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      <!-- 移动端 行业应用 -->\r\n      <section class=\"mobile-section mobile-applications-section\">\r\n        <div class=\"mobile-section-header\">\r\n          <h2 class=\"mobile-section-title\">行业应用</h2>\r\n          <p class=\"mobile-section-description\">\r\n            Applications\r\n          </p>\r\n        </div>\r\n\r\n        <div class=\"mobile-applications-grid\">\r\n          <div class=\"mobile-app-item\" v-for=\"(app, index) in mobileApplications\" :key=\"index\">\r\n            <div class=\"mobile-app-image\">\r\n              <img :src=\"app.image\" :alt=\"app.title\">\r\n            </div>\r\n            <h3 class=\"mobile-app-title\">{{ app.title }}</h3>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      <!-- 移动端 咨询按钮 -->\r\n      <div class=\"mobile-consult-section\">\r\n        <h3 class=\"mobile-consult-title\">为AI+千行百业，提供高性能算力服务</h3>\r\n        <button class=\"mobile-consult-button\" @click=\"openContactModal\">立即咨询</button>\r\n      </div>\r\n\r\n      <!-- 移动端 联系弹窗 -->\r\n      <transition name=\"mobile-fade\">\r\n        <div v-if=\"showContactModal\" class=\"mobile-contact-overlay\" @click.self=\"closeContactModal\">\r\n          <div class=\"mobile-contact-modal\">\r\n            <button class=\"mobile-close-modal\" @click=\"closeContactModal\">\r\n              &times;\r\n            </button>\r\n            <div class=\"mobile-contact-content\">\r\n              <div class=\"mobile-contact-item\">\r\n                <i class=\"am-icon-user\"></i>\r\n                <span>{{ contactInfo.name }}</span>\r\n              </div>\r\n              <div class=\"mobile-contact-item\">\r\n                <i class=\"am-icon-phone\"></i>\r\n                <span>{{ contactInfo.phone }}</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"mobile-contact-note\">\r\n              <p>欢迎随时来电咨询</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </transition>\r\n    </div>\r\n    <Mider v-if=\"!isMobile\"></Mider>\r\n    <Footer></Footer>\r\n    <chatAi></chatAi>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// import Layout from \"@/components/common/Layout\";\r\nimport chatAi from \"@/components/common/mider/chatAi\";\r\nimport Mider from \"@/components/common/mider/Mider\";\r\nimport Footer from \"@/components/common/footer/Footer\";\r\nimport GpuComparison from \"@/views/Index/GpuComparison\";\r\nimport {postAnyData, getNotAuth, postNotAuth,getAnyData,postJsonData} from \"@/api/login\";\r\n\r\n\r\nimport {setToken} from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"IndexView\",\r\n  components: { chatAi,Footer, Mider, GpuComparison },\r\n  computed: {\r\n    translate() {\r\n      return -this.translateX * 100 + \"%\";\r\n    },\r\n    isLogin() {\r\n      return !! document.cookie.includes('Admin-Token');\r\n    },\r\n    mobileTranslateX() {\r\n      return -this.mobileCurrentSlide * 100;\r\n    },\r\n    mobileApplications() {\r\n      return [\r\n        this.firstRowWide,\r\n        ...this.firstRowTallApps,\r\n        ...this.secondRowApps,\r\n        ...this.thirdRowSmallApps,\r\n        this.thirdRowWide\r\n      ];\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      touchStartX: 0,\r\n      touchEndX: 0,\r\n      touchThreshold: 50, // 滑动阈值，单位px\r\n      isMobile: false,\r\n      mobileCurrentSlide: 0,\r\n      showContactModal: false,\r\n      contactInfo: {\r\n        name: '王先生',\r\n        phone: '13913283376'\r\n      },\r\n      tabIndex: 0,\r\n      firstRowWide: {\r\n        title: '工业制造',\r\n        image: require(\"../../assets/images/index/gongyezhizao.webp\"),\r\n        hover: false,\r\n      },\r\n      firstRowTallApps: [\r\n        {\r\n          title: '自动驾驶',\r\n          image: require(\"../../assets/images/index/zidongjiashi.webp\"),\r\n          hover: false,\r\n        },\r\n        {\r\n          title: '智能交通',\r\n          image: require(\"../../assets/images/index/zhinengjiaotong.webp\"),\r\n          hover: false,\r\n        }\r\n      ],\r\n      secondRowApps: [\r\n        {\r\n          title: '智慧农业',\r\n          image: require(\"../../assets/images/index/zhihuinongye.webp\"),\r\n          hover: false,\r\n        },\r\n        {\r\n          title: '影视渲染',\r\n          image: require(\"../../assets/images/index/yingshixuanran.webp\"),\r\n          hover: false,\r\n        }\r\n      ],\r\n      thirdRowSmallApps: [\r\n        {\r\n          title: '医疗影像',\r\n          image: require(\"../../assets/images/index/yiliaoyingxiang.webp\"),\r\n          hover: false,\r\n        },\r\n        {\r\n          title: '金融风暴',\r\n          image: require(\"../../assets/images/index/jinrongfengbao.webp\"),\r\n          hover: false,\r\n        }\r\n      ],\r\n      thirdRowWide: {\r\n        title: '能源科技',\r\n        image: require(\"../../assets/images/index/nengyuankeji.webp\"),\r\n        hover: false,\r\n      },\r\n      bannerImages: [\r\n        {\r\n          img: require(\"/public/images/back1.webp\"),\r\n          content: {\r\n            title: \"天工开物\",\r\n            text: \"构建AI应用周期服务的一站式算力云\",\r\n            position: \"left\",\r\n            thirdLink: \"/product\",\r\n            thirdBtnText: \"立即购买\"\r\n          }\r\n        },\r\n        {\r\n          img: require(\"/public/images/back2.webp\"),\r\n          content: {\r\n            title: \"专业AI训练平台\",\r\n            text: \"为AI+千行百业，提供高性能算力服务\",\r\n            position: \"left\",\r\n            secondaryLink: \"/login\",\r\n            secondaryBtnText: \"立即登录\",\r\n            primaryLink: '/register',\r\n            primaryBtnText: \"立即注册\"\r\n          }\r\n        },\r\n        {\r\n          img: require(\"/public/images/back3.webp\"),\r\n          content: {\r\n            title: \"企业级GPU集群\",\r\n            text: \"H100/H800/RTX 4090等高性能GPU随时可用，按需付费\",\r\n            position: \"left\",\r\n          }\r\n        }\r\n      ],\r\n      translateX: 0,\r\n      tsion: true,\r\n      tabList: [],\r\n      swiperOptions: {\r\n        loop: true,\r\n        autoplay: {\r\n          delay: 5000,\r\n          disableOnInteraction: false,\r\n        },\r\n        pagination: {\r\n          el: '.swiper-pagination',\r\n          clickable: true\r\n        },\r\n        navigation: {\r\n          nextEl: '.swiper-button-next',\r\n          prevEl: '.swiper-button-prev'\r\n        }\r\n      },\r\n      serviceList: [\r\n        { id: 1, icon: 'am-icon-shield', title: '数据安全', desc: '平台采取各种措施保证用户的数据安全，例如数据加密、防火墙、漏洞扫描和安全审计等措施，以防止数据泄露、篡改和丢失等风险。' },\r\n        { id: 2, icon: 'am-icon-sliders', title: '部署灵活', desc: '租用GPU服务器具备比购买更高的灵活性，用户可以根据自己的需求随时调整租赁资源的配置及数量。' },\r\n        { id: 3, icon: 'am-icon-server', title: '高可靠性', desc: '平台拥有完善的运维体系，采用丰富的数据备份、冗余技术以及定期检测等机制，保证租赁服务器的稳定性、易用性和安全性。' },\r\n        { id: 4, icon: 'am-icon-rocket', title: '高处理性能', desc: '采用先进的GPU集群架构，平台能够大大提高计算速度和处理能力，可以在科学计算、深度学习等领域有更优秀的表现。' },\r\n        { id: 5, icon: 'am-icon-credit-card', title: '低成本', desc: '购买GPU服务器需要投入较高的资金，而租赁可以让用户以较低的成本去获得相关基础设施，减轻了用户在预算上的负担。' },\r\n        { id: 6, icon: 'am-icon-phone', title: '及时服务', desc: '提供365天 7*24小时技术支持及运维服务， 工程师现场及在线响应及电话支持。' }\r\n      ],\r\n      comparisonGpus: [\r\n        {\r\n          name: \"A100\",\r\n          architecture: \"Ampere\",\r\n          fp16Performance: \"312 TFLOPS\",\r\n          fp32Performance: \"19.5 TFLOPS\",\r\n          memory: \"80 GB\",\r\n          memoryType: \"HBM2\",\r\n          bandwidth: \"2,039 GB/s\"\r\n        },\r\n        {\r\n          name: \"A100\",\r\n          architecture: \"Ampere\",\r\n          fp16Performance: \"312 TFLOPS\",\r\n          fp32Performance: \"19.5 TFLOPS\",\r\n          memory: \"80 GB\",\r\n          memoryType: \"HBM2\",\r\n          bandwidth: \"2,039 GB/s\"\r\n        },\r\n        {\r\n          name: \"A100\",\r\n          architecture: \"Ampere\",\r\n          fp16Performance: \"312 TFLOPS\",\r\n          fp32Performance: \"19.5 TFLOPS\",\r\n          memory: \"80 GB\",\r\n          memoryType: \"HBM2\",\r\n          bandwidth: \"2,039 GB/s\"\r\n        },\r\n        {\r\n          name: \"V100\",\r\n          architecture: \"Volta\",\r\n          fp16Performance: \"125 TFLOPS\",\r\n          fp32Performance: \"15.7 TFLOPS\",\r\n          memory: \"32 GB\",\r\n          memoryType: \"HBM2\",\r\n          bandwidth: \"900 GB/s\"\r\n        },\r\n        {\r\n          name: \"A6000\",\r\n          architecture: \"Ampere\",\r\n          fp16Performance: \"77.4 TFLOPS\",\r\n          fp32Performance: \"38.7 TFLOPS\",\r\n          memory: \"48 GB\",\r\n          memoryType: \"GDDR6\",\r\n          bandwidth: \"768 GB/s\"\r\n        },\r\n        {\r\n          name: \"A5000\",\r\n          architecture: \"Ampere\",\r\n          fp16Performance: \"54.2 TFLOPS\",\r\n          fp32Performance: \"27.8 TFLOPS\",\r\n          memory: \"24 GB\",\r\n          memoryType: \"GDDR6\",\r\n          bandwidth: \"768 GB/s\"\r\n        },\r\n        {\r\n          name: \"A4000\",\r\n          architecture: \"Ampere\",\r\n          fp16Performance: \"19.17 TFLOPS\",\r\n          fp32Performance: \"19.17 TFLOPS\",\r\n          memory: \"16 GB\",\r\n          memoryType: \"GDDR6\",\r\n          bandwidth: \"448 GB/s\"\r\n        }\r\n      ],\r\n      gpus: [\r\n        {\r\n          name: \"A100 SXM4 / 80GB\",\r\n          singlePrecision: \"156\",\r\n          halfPrecision: \"19.5\",\r\n          originalPrice: \"11.10\",\r\n          price: \"6.66\",\r\n          recommended: true,\r\n          isNew: false\r\n        },\r\n        {\r\n          name: \"RTX 3090 / 24GB\",\r\n          singlePrecision: \"35.58\",\r\n          halfPrecision: \"71.2\",\r\n          originalPrice: \"2.30\",\r\n          price: \"1.38\",\r\n          recommended: true,\r\n          isNew: false\r\n        },\r\n        {\r\n          name: \"A100 PCIE / 80GB\",\r\n          singlePrecision: \"156\",\r\n          halfPrecision: \"19.5\",\r\n          originalPrice: \"11.10\",\r\n          price: \"6.66\",\r\n          recommended: true,\r\n          isNew: false\r\n        },\r\n        {\r\n          name: \"RTX 4090 / 24GB\",\r\n          singlePrecision: \"82.58\",\r\n          halfPrecision: \"164.5\",\r\n          originalPrice: \"2.97\",\r\n          price: \"1.78\",\r\n          recommended: false,\r\n          isNew: true\r\n        },\r\n        {\r\n          name: \"RTX 4090D / 24GB\",\r\n          singlePrecision: \"73.54\",\r\n          halfPrecision: \"147.1\",\r\n          originalPrice: \"2.93\",\r\n          price: \"1.76\",\r\n          recommended: false,\r\n          isNew: false\r\n        },\r\n        {\r\n          name: \"RTX 3060 / 12GB\",\r\n          singlePrecision: \"12.7\",\r\n          halfPrecision: \"51.2\",\r\n          originalPrice: \"1.00\",\r\n          price: \"0.60\",\r\n          recommended: false,\r\n          isNew: false\r\n        },\r\n        {\r\n          name: \"RTX A4000 / 16GB\",\r\n          singlePrecision: \"19.17\",\r\n          halfPrecision: \"76.7\",\r\n          originalPrice: \"1.53\",\r\n          price: \"0.92\",\r\n          recommended: false,\r\n          isNew: false\r\n        },\r\n        {\r\n          name: \"Tesla P40 / 24GB\",\r\n          singlePrecision: \"5.9\",\r\n          halfPrecision: \"11.76\",\r\n          originalPrice: \"1.35\",\r\n          price: \"0.81\",\r\n          recommended: false,\r\n          isNew: false\r\n        },\r\n\r\n      ],\r\n      activeIndex: 0,\r\n    };\r\n  },\r\n  created() {\r\n    this.fetchRecommendations();\r\n    const url = new URL(window.location.href);\r\n    const token = url.searchParams.get('token');\r\n\r\n    if (token) {\r\n      const hasRefreshed = localStorage.getItem('hasRefreshedWithToken');\r\n      // console.log('需要修改的参数为', url.origin + url.pathname)\r\n      if (!hasRefreshed) {\r\n        setToken(token);\r\n        localStorage.setItem('hasRefreshedWithToken', 'true');\r\n        // 直接修改 window.location（移除所有查询参数）\r\n        // console.log('需要修改的参数为', url.origin + url.pathname)\r\n\r\n      } else {\r\n        window.location.href = url.origin + url.pathname;\r\n        localStorage.setItem('hasRefreshedWithToken', 'false');\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.checkIsMobile();\r\n    window.addEventListener('resize', this.checkIsMobile);\r\n\r\n    // Banner auto-play\r\n    this.desktopInterval = setInterval(() => {\r\n      this.next();\r\n    }, 5000);\r\n\r\n    this.mobileInterval = setInterval(() => {\r\n      this.mobileNext();\r\n    }, 5000);\r\n  },\r\n  beforeDestroy() {\r\n    window.removeEventListener('resize', this.checkIsMobile);\r\n    clearInterval(this.desktopInterval);\r\n    clearInterval(this.mobileInterval);\r\n\r\n  },\r\n  methods: {\r\n\r\n    async fetchRecommendations() {\r\n      try {\r\n        getNotAuth(\"/system/recommend/list\").then(req =>{\r\n          this.gpus = req.data.rows.map(item => ({\r\n            ...item,\r\n            isNew: item.isnew === 0,\r\n            recommended: item.recommended === 0\r\n          }))\r\n          // this.gpus = req.data.rows\r\n        })\r\n      } catch (error) {\r\n        console.error('获取GPU推荐列表失败:', error);\r\n      }\r\n    },\r\n\r\n    handleTouchStart(e) {\r\n      this.touchStartX = e.touches[0].clientX;\r\n    },\r\n\r\n    handleTouchMove(e) {\r\n      this.touchEndX = e.touches[0].clientX;\r\n    },\r\n\r\n    handleTouchEnd() {\r\n      if (!this.touchStartX || !this.touchEndX) return;\r\n\r\n      const diff = this.touchStartX - this.touchEndX;\r\n\r\n      // 向右滑动\r\n      if (diff > this.touchThreshold) {\r\n        this.mobileNext();\r\n      }\r\n\r\n      // 向左滑动\r\n      if (diff < -this.touchThreshold) {\r\n        this.mobilePrev();\r\n      }\r\n\r\n      // 重置触摸位置\r\n      this.touchStartX = 0;\r\n      this.touchEndX = 0;\r\n    },\r\n\r\n    mobilePrev() {\r\n      this.mobileCurrentSlide = (this.mobileCurrentSlide - 1 + this.bannerImages.length) % this.bannerImages.length;\r\n      this.resetMobileInterval();\r\n    },\r\n\r\n    mobileNext() {\r\n      this.mobileCurrentSlide = (this.mobileCurrentSlide + 1) % this.bannerImages.length;\r\n      this.resetMobileInterval();\r\n    },\r\n\r\n    resetMobileInterval() {\r\n      clearInterval(this.mobileInterval);\r\n      this.mobileInterval = setInterval(() => {\r\n        this.mobileNext();\r\n      }, 5000);\r\n    },\r\n    checkIsMobile() {\r\n      this.isMobile = window.innerWidth <= 768;\r\n    },\r\n    openContactModal() {\r\n      this.showContactModal = true;\r\n    },\r\n    closeContactModal() {\r\n      this.showContactModal = false;\r\n    },\r\n    handleBannerAction(item) {\r\n      if (item.content.link) {\r\n        this.$router.push(item.content.link);\r\n      }\r\n      this.$ga.event('Banner', 'click', item.content.title);\r\n    },\r\n    navigateTo(path) {\r\n      if (this.currentPath && this.currentPath !== path) {\r\n        this.previousActivePath = this.currentPath;\r\n\r\n        this.$nextTick(() => {\r\n          const navLinks = document.querySelectorAll('.nav-link, .btn-login');\r\n          navLinks.forEach(link => {\r\n            if ((link.classList.contains('active') ||\r\n                    (path === '/login' && link.classList.contains('btn-login'))) &&\r\n                !link.classList.contains('active-exit')) {\r\n              link.classList.add('active-exit');\r\n\r\n              setTimeout(() => {\r\n                link.classList.remove('active-exit');\r\n              }, 300);\r\n            }\r\n          });\r\n\r\n          this.currentPath = path;\r\n        });\r\n      } else {\r\n        this.currentPath = path;\r\n      }\r\n\r\n      if (this.$route.path === path) {\r\n        this.$nextTick(() => {\r\n          window.scrollTo({\r\n            top: 0,\r\n            behavior: 'instant'\r\n          });\r\n          this.$router.go(0);\r\n        });\r\n      } else {\r\n        this.$router.push(path);\r\n        window.scrollTo({\r\n          top: 0,\r\n          behavior: 'instant'\r\n        });\r\n      }\r\n    },\r\n    last() {\r\n      this.translateX = (this.translateX - 1 + this.bannerImages.length) % this.bannerImages.length;\r\n      this.tsion = true;\r\n    },\r\n    next() {\r\n      this.translateX = (this.translateX + 1) % this.bannerImages.length;\r\n      this.tsion = true;\r\n    },\r\n\r\n    goToSlide(index) {\r\n      this.mobileCurrentSlide = index;\r\n      // 重置自动播放计时器\r\n      clearInterval(this.mobileInterval);\r\n      this.mobileInterval = setInterval(() => {\r\n        this.mobileNext();\r\n      }, 5000);\r\n    },\r\n    changeTab(index) {\r\n      this.tabIndex = index;\r\n    },\r\n    useGpu(gpu) {\r\n      console.log(`Selected GPU: ${gpu.name}`);\r\n    },\r\n    getCustomSolution() {\r\n      console.log('Get Custom Solution');\r\n    },\r\n    contactUs() {\r\n      console.log('Contact Us');\r\n    },\r\n    prevSlide() {\r\n      this.activeIndex = (this.activeIndex > 0) ? this.activeIndex - 1 : this.slideshow.length - 1;\r\n    },\r\n    nextSlide() {\r\n      this.activeIndex = (this.activeIndex < this.slideshow.length - 1) ? this.activeIndex + 1 : 0;\r\n    },\r\n    selectGpu(gpu) {\r\n      console.log(`Selected GPU: ${gpu.name}`);\r\n    },\r\n  },\r\n  watch: {\r\n    translateX(newVal) {\r\n      const dots = this.$refs.swiperPagination?.querySelectorAll(\"span\");\r\n      if (dots) {\r\n        dots.forEach((dot, index) => {\r\n          dot.classList.toggle(\"active\", index === newVal);\r\n        });\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 通用样式 */\r\n.section-header {\r\n  text-align: center;\r\n  margin-bottom: 40px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 28px;\r\n  color: #333;\r\n  position: relative;\r\n  display: inline-block;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.section-title::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: -5px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 50px;\r\n  height: 3px;\r\n  background-color: #2196f3;\r\n}\r\n\r\n.section-description {\r\n  color: #666;\r\n  max-width: 600px;\r\n  margin: 0 auto;\r\n  line-height: 1.6;\r\n}\r\n\r\n/* 电脑端样式 */\r\n.desktop-layout {\r\n  display: block;\r\n}\r\n\r\n/* Banner Section */\r\n.banner-section {\r\n  padding: 0;\r\n}\r\n\r\n.banner-container {\r\n  width: 100%;\r\n  max-width: 2560px;\r\n  max-height: 1920px;\r\n  margin: 0;\r\n  position: relative;\r\n}\r\n\r\n.big-box {\r\n  width: 100%;\r\n  height: 93vh;\r\n  overflow: hidden;\r\n  position: relative;\r\n  box-shadow: 0 4px 20px rgba(0,0,0,0.15);\r\n}\r\n\r\n.img-box {\r\n  height: 100%;\r\n  width: 100%;\r\n}\r\n\r\n.show-box {\r\n  display: flex;\r\n  height: 100%;\r\n  width: 100%;\r\n  transition: all 0.5s;\r\n}\r\n\r\n.slide-item {\r\n  position: relative;\r\n  flex-shrink: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.slide-item img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.banner-content {\r\n  position: absolute;\r\n  top: 40%;\r\n  background-color: rgba(0, 0, 0, 0);\r\n  padding: 40px;\r\n  font-weight: 500;\r\n  box-shadow: 0 5px 20px rgba(0, 0, 0, 0);\r\n  max-width: 800px;\r\n  color: #fdfcfc;\r\n  z-index: 4;\r\n}\r\n\r\n.banner-title {\r\n  font-size: 7vh;\r\n  line-height: 1;\r\n  margin-bottom: 0px;\r\n  font-weight: 700;\r\n}\r\n\r\n.banner-text {\r\n  font-size: 3vh;\r\n  line-height: 3.5;\r\n  margin-bottom: 0px;\r\n  opacity: 0.9;\r\n}\r\n\r\n.banner-actions {\r\n  display: flex;\r\n  gap: 20px;\r\n  z-index: 20;\r\n}\r\n\r\n.banner-button {\r\n  padding: 10px 5px;\r\n  border-radius: 0px;\r\n  font-size: 1.5rem;\r\n  font-weight:200;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 4px 10px rgba(248, 245, 245, 0.2);\r\n  text-decoration: none;\r\n  text-align: center;\r\n  display: inline-block;\r\n  background-color: transparent;\r\n  border: 2px solid white;\r\n  color: white;\r\n  position: relative;\r\n  z-index: 3;\r\n  pointer-events: auto;\r\n  width: 150px;\r\n}\r\n\r\n.banner-button:hover {\r\n  background-color: black;\r\n}\r\n\r\n.primary-btn {\r\n  color: white;\r\n  border: 1px solid white;\r\n}\r\n\r\n.primary-btn:hover {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.secondary-btn {\r\n  color: white;\r\n  background-color: #f67b3d;\r\n  border-color: #f67b3d;\r\n}\r\n\r\n.secondary-btn:hover {\r\n  background-color: #f67b3d;\r\n}\r\n\r\n.pos-left {\r\n  left: 8%;\r\n  transform: translateY(-50%);\r\n  text-align: left;\r\n  animation: slideInFromLeft 0.8s ease;\r\n}\r\n\r\n.pos-right {\r\n  right: 10%;\r\n  transform: translateY(-50%);\r\n  text-align: left;\r\n  animation: slideInFromRight 0.8s ease;\r\n}\r\n\r\n.pos-center {\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  text-align: center;\r\n  animation: fadeIn 0.8s ease;\r\n}\r\n\r\n.arrowhead-box {\r\n  position: absolute;\r\n  top: 50%;\r\n  width: 100%;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  transform: translateY(-50%);\r\n  padding: 0 20px;\r\n  z-index: 10;\r\n  pointer-events: none;\r\n}\r\n\r\n.nav-arrow {\r\n  display: flex;\r\n  pointer-events: auto;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 50%;\r\n  background: rgba(0,0,0,0.3);\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.nav-arrow:hover {\r\n  background: rgba(0,0,0,0.5);\r\n}\r\n\r\n.arrow-icon {\r\n  height: 24px;\r\n  width: 24px;\r\n  transition: transform 0.3s;\r\n  filter: invert(1);\r\n}\r\n\r\n.rotated {\r\n  transform: rotate(180deg);\r\n}\r\n\r\n.swiper-pagination {\r\n  position: absolute;\r\n  bottom: 20px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  display: flex;\r\n  gap: 10px;\r\n  z-index: 3;\r\n}\r\n\r\n.swiper-pagination span {\r\n  width: 12px;\r\n  height: 12px;\r\n  background: rgba(255,255,255,0.5);\r\n  border-radius: 50%;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.swiper-pagination span.active {\r\n  width: 30px;\r\n  border-radius: 6px;\r\n  background: #fff;\r\n}\r\n\r\n/* GPU Section */\r\n.gpu-specs-pricing {\r\n  display: flex;\r\n  height: 8vh;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 0px;\r\n}\r\n\r\n.specs-section {\r\n  flex: 1;\r\n}\r\n\r\n.price-section {\r\n  text-align: right;\r\n  min-width: 120px;\r\n}\r\n\r\n.gpu-pricing {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-end;\r\n}\r\n\r\n.original-price {\r\n  text-decoration: line-through;\r\n  color: #999;\r\n  font-size: 14px;\r\n}\r\n\r\n.current-price {\r\n  font-size: 16px;\r\n  color: #333;\r\n}\r\n\r\n.price-value {\r\n  font-size: 24px;\r\n  font-weight: 700;\r\n  color: #2196f3;\r\n}\r\n\r\n/* GPU Card Grid */\r\n.gpu-section {\r\n  background-color: #f9f9f9;\r\n  width: 100%;\r\n  max-width: 2560px;\r\n  max-height: 800px;\r\n  margin: 0 auto;\r\n  /*margin-bottom: -10vh;*/\r\n\r\n}\r\n\r\n.gpu-card-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(3, 1fr);\r\n  gap: 20px;\r\n  padding: 0 50px;\r\n  margin-bottom: 40px;\r\n}\r\n\r\n.gpu-card {\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);\r\n  padding: 20px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: relative;\r\n  transition: all 0.3s ease;\r\n  border: 1px solid transparent;\r\n}\r\n\r\n.gpu-card:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);\r\n  border: 1px solid #2196f3;\r\n}\r\n\r\n.gpu-card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.gpu-name {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-left: 0px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.recommendation-tag {\r\n  background-color: #2196f3;\r\n  color: white;\r\n  font-size: 12px;\r\n  padding: 3px 8px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.new-tag {\r\n  background-color: #ff4d4f;\r\n  color: white;\r\n  font-size: 12px;\r\n  padding: 3px 8px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.spec-item {\r\n  display: flex;\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n}\r\n\r\n.spec-label {\r\n  color: #666;\r\n  margin-right: 5px;\r\n  flex-shrink: 0;\r\n  width: 60px;\r\n}\r\n\r\n.spec-value {\r\n  color: #333;\r\n  font-weight: 500;\r\n}\r\n\r\n/* GPU 性能对比 Section */\r\n.gpu-comparison-section {\r\n  background-color: #f9f9f9;\r\n  width: 100%;\r\n  max-width: 2560px;\r\n  margin: 0 auto;\r\n  margin-bottom: -12vh;\r\n}\r\n\r\n.gpu-comparison-table {\r\n  width: 100%;\r\n  padding: 0px 50px;\r\n  border-collapse: collapse;\r\n  margin: 0;\r\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);\r\n  border-radius: 10px;\r\n  overflow:hidden;\r\n}\r\n\r\n.gpu-comparison-table thead {\r\n  background-color: #cddfec;\r\n}\r\n\r\n.gpu-comparison-table thead tr th {\r\n  padding: 15px 20px;\r\n  text-align: center;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.gpu-comparison-table tbody tr td {\r\n  padding: 15px 20px;\r\n  text-align: center;\r\n  border-bottom: 1px solid #e0e5eb;\r\n  transition: background-color 0.3s ease;\r\n  font-size: 15px;\r\n  color: #333;\r\n}\r\n\r\n.gpu-comparison-table tbody tr:nth-child(even) {\r\n  background-color: #f9fafc;\r\n}\r\n\r\n.gpu-comparison-table tbody tr:hover {\r\n  background-color: #f1f6fd;\r\n}\r\n\r\n/* Services Section */\r\n.services-section {\r\n  background-color: #f9f9f9;\r\n  width: 100%;\r\n  max-width: 2560px;\r\n}\r\n\r\n.services-grid {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  padding: 0px 50px;\r\n}\r\n\r\n.service-item {\r\n  width: 33.33%;\r\n  padding: 0 11px;\r\n}\r\n\r\n.service-item:nth-child(-n+3) {\r\n  margin-bottom: 40px;\r\n}\r\n\r\n.service-card {\r\n  background-color: #fff;\r\n  border-radius: 10px;\r\n  box-shadow: 0 5px 15px rgba(0,0,0,0.05);\r\n  padding: 25px;\r\n  height: 100%;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  text-align: left;\r\n}\r\n\r\n.service-card:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 10px 25px rgba(0,0,0,0.1);\r\n}\r\n\r\n.service-icon {\r\n  font-size: 48px;\r\n  margin-bottom: -30px;\r\n  color: #2196f3;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.service-card:hover .service-icon {\r\n  transform: scale(1.1);\r\n}\r\n\r\n.service-title {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.service-text {\r\n  font-size: 14px;\r\n  color: #666;\r\n  line-height: 2;\r\n}\r\n\r\n/* Application Areas Section */\r\n.appsec-section {\r\n  background-color: #f8f9fa;\r\n  max-width: 2560px;\r\n  max-height: 1000px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.appsec-container {\r\n  margin: 0 auto;\r\n  max-height: 1000px;\r\n  width: 100%;\r\n  padding: 0 50px;\r\n}\r\n\r\n.appsec-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(4, 1fr);\r\n  grid-auto-rows: minmax(180px, auto);\r\n  gap: 20px;\r\n}\r\n\r\n.appsec-item {\r\n  position: relative;\r\n  overflow: hidden;\r\n  border-radius: 4px;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.appsec-wide {\r\n  grid-column: span 2;\r\n  grid-row: span 1;\r\n  height: 180px;\r\n}\r\n\r\n.appsec-tall {\r\n  grid-column: span 1;\r\n  grid-row: span 2;\r\n  height: 380px;\r\n}\r\n\r\n.appsec-small {\r\n  grid-column: span 1;\r\n  grid-row: span 1;\r\n  height: 180px;\r\n}\r\n\r\n.appsec-card {\r\n  position: relative;\r\n  height: 100%;\r\n  width: 100%;\r\n  cursor: pointer;\r\n  overflow: hidden;\r\n}\r\n\r\n.appsec-image {\r\n  height: 100%;\r\n  width: 100%;\r\n  transition: transform 0.5s ease;\r\n}\r\n\r\n.appsec-image img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  transition: transform 0.5s ease;\r\n}\r\n\r\n.appsec-cardtitle {\r\n  position: absolute;\r\n  bottom: 20px;\r\n  left: 20px;\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);\r\n  z-index: 2;\r\n  transition: transform 0.3s ease, font-size 0.3s ease;\r\n}\r\n\r\n.appsec-card::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 50%;\r\n  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);\r\n  z-index: 1;\r\n}\r\n\r\n.appsec-hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n/* Recommendation Section */\r\n.recommendation-tag1 {\r\n  margin-top: 3vh;\r\n  position: relative;\r\n  width: 100%;\r\n  height: 120px;\r\n  background-image: url(\"../../assets/images/index/back3.png\");\r\n  overflow: hidden;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.card1 {\r\n  position: relative;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  width: 100%;\r\n  max-width: 1200px;\r\n  padding: 0 20px;\r\n  z-index: 2;\r\n}\r\n\r\n.banner-text1 {\r\n  color: white;\r\n  font-size: 2rem;\r\n  font-weight: bold;\r\n  margin: 0;\r\n  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.consult-button1 {\r\n  background-color: white;\r\n  color: #0d47a1;\r\n  border: none;\r\n  border-radius: 25px;\r\n  padding: 10px 25px;\r\n  font-size: 1.7rem;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.consult-button1:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\r\n  background-color: #0a69ff;\r\n  color: white;\r\n}\r\n\r\n/* Contact Modal */\r\n.contact-modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 1000;\r\n}\r\n\r\n.contact-modal {\r\n  position: relative;\r\n  background-color: white;\r\n  padding: 30px;\r\n  border-radius: 8px;\r\n  width: 90%;\r\n  max-width: 250px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n  text-align: center;\r\n}\r\n\r\n.close-modal {\r\n  position: absolute;\r\n  top: 10px;\r\n  right: 15px;\r\n  font-size: 30px;\r\n  background: none;\r\n  border: none;\r\n  cursor: pointer;\r\n  color: #999;\r\n}\r\n\r\n.contact-content {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.contact-item {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-bottom: 15px;\r\n  font-size: 18px;\r\n}\r\n\r\n.contact-item i {\r\n  margin-right: 10px;\r\n  color: #2196f3;\r\n}\r\n\r\n.contact-note {\r\n  color: #666;\r\n  font-size: 14px;\r\n  line-height: 1.6;\r\n}\r\n\r\n/* Animations */\r\n@keyframes slideInFromLeft {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateX(-50px) translateY(-50%);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateX(0) translateY(-50%);\r\n  }\r\n}\r\n\r\n@keyframes slideInFromRight {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateX(50px) translateY(-50%);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateX(0) translateY(-50%);\r\n  }\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n  }\r\n  to {\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n/* 移动端样式 */\r\n.mobile-layout {\r\n  display: none;\r\n}\r\n\r\n/* 移动端响应式设计 */\r\n@media (max-width: 768px) {\r\n  .desktop-layout {\r\n    display: none;\r\n\r\n  }\r\n  .container, .banner-section, .services-grid, .appsec-container {\r\n    max-width: 10%;\r\n    padding-left: 15px;\r\n    padding-right: 15px;\r\n    overflow-x: hidden;\r\n  }\r\n\r\n  .mobile-layout {\r\n    display: block;\r\n    overflow-x: hidden;\r\n\r\n    /*padding: 0 15px;*/\r\n  }\r\n\r\n\r\n    /* 修改移动端轮播图样式 */\r\n  .mobile-banner {\r\n    position: relative;\r\n    width: 100%;\r\n    height: 100%;\r\n    min-height: 30vh;\r\n    overflow: hidden;\r\n    touch-action: pan-y;\r\n    user-select: none;\r\n    /*margin-bottom: -5vh;*/\r\n  }\r\n\r\n  .mobile-banner-slider {\r\n    display: flex;\r\n    height: 100%;\r\n    transition: transform 0.5s ease;\r\n  }\r\n\r\n  .mobile-slide {\r\n    flex: 0 0 100%;\r\n    width: 100%;\r\n    height: 70%;\r\n    position: relative;\r\n  }\r\n\r\n  .mobile-slide-inner {\r\n    width: 100%;\r\n    height: 100%;\r\n    position: relative;\r\n  }\r\n\r\n  .mobile-slide-img {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n  }\r\n\r\n  .mobile-banner-content {\r\n    position: absolute;\r\n    bottom: 0;\r\n    left: 0;\r\n    right: 0;\r\n    padding: 15px;\r\n    /*background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);*/\r\n    color: white;\r\n    z-index: 2;\r\n  }\r\n\r\n  .mobile-banner-title {\r\n    font-size: 1.5rem;\r\n    margin-bottom: 8px;\r\n    font-weight: bold;\r\n    line-height: 1.3;\r\n  }\r\n\r\n  .mobile-banner-text {\r\n    font-size: 0.9rem;\r\n    margin-bottom: 12px;\r\n    line-height: 1.4;\r\n  }\r\n\r\n  .mobile-banner-actions {\r\n    display: flex;\r\n    gap: 10px;\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .mobile-banner-button {\r\n    padding: 8px 12px;\r\n    font-size: 14px;\r\n    border-radius: 4px;\r\n    text-decoration: none;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .mobile-banner-button.primary-btn {\r\n    background-color: transparent;\r\n    border: 1px solid white;\r\n    color: white;\r\n  }\r\n\r\n  .mobile-banner-button.secondary-btn {\r\n    background-color: #f67b3d;\r\n    color: white;\r\n    border: none;\r\n  }\r\n\r\n  .mobile-banner-pagination {\r\n    position: absolute;\r\n    bottom: 10px;\r\n    left: 0;\r\n    right: 0;\r\n    display: flex;\r\n    justify-content: center;\r\n    gap: 8px;\r\n    z-index: 3;\r\n  }\r\n\r\n  .mobile-banner-pagination span {\r\n    display: block;\r\n    width: 8px;\r\n    height: 8px;\r\n    border-radius: 50%;\r\n    background-color: rgba(255,255,255,0.5);\r\n    transition: all 0.3s ease;\r\n    cursor: pointer;\r\n  }\r\n\r\n  .mobile-banner-pagination span.active {\r\n    background-color: white;\r\n    width: 20px;\r\n    border-radius: 4px;\r\n  }\r\n\r\n\r\n\r\n  /* 移动端 GPU 推荐 */\r\n  .mobile-section {\r\n    padding: 30px 0;\r\n  }\r\n\r\n  .mobile-section-header {\r\n    text-align: center;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .mobile-section-title {\r\n    font-size: 22px;\r\n    color: #333;\r\n    position: relative;\r\n    display: inline-block;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .mobile-section-title::after {\r\n    content: '';\r\n    position: absolute;\r\n    bottom: -5px;\r\n    left: 50%;\r\n    transform: translateX(-50%);\r\n    width: 40px;\r\n    height: 2px;\r\n    background-color: #2196f3;\r\n  }\r\n\r\n  .mobile-section-description {\r\n    font-size: 14px;\r\n    color: #666;\r\n    max-width: 80%;\r\n    margin: 0 auto;\r\n  }\r\n\r\n  .mobile-gpu-list {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 15px;\r\n  }\r\n\r\n  .mobile-gpu-card {\r\n    background-color: white;\r\n    border-radius: 8px;\r\n    padding: 15px;\r\n    box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n  }\r\n\r\n  .mobile-gpu-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .mobile-gpu-name {\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #333;\r\n    margin: 0;\r\n  }\r\n\r\n  .mobile-gpu-tags {\r\n    display: flex;\r\n    gap: 5px;\r\n  }\r\n\r\n  .mobile-recommend-tag {\r\n    background-color: #2196f3;\r\n    color: white;\r\n    font-size: 10px;\r\n    padding: 2px 6px;\r\n    border-radius: 3px;\r\n  }\r\n\r\n  .mobile-new-tag {\r\n    background-color: #ff4d4f;\r\n    color: white;\r\n    font-size: 10px;\r\n    padding: 2px 6px;\r\n    border-radius: 3px;\r\n  }\r\n\r\n  .mobile-gpu-specs {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .mobile-spec-item {\r\n    display: flex;\r\n    margin-bottom: 5px;\r\n    font-size: 13px;\r\n  }\r\n\r\n  .mobile-spec-label {\r\n    color: #666;\r\n    margin-right: 5px;\r\n    width: 60px;\r\n  }\r\n\r\n  .mobile-spec-value {\r\n    color: #333;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .mobile-gpu-price {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n  }\r\n\r\n  .mobile-original-price {\r\n    font-size: 12px;\r\n    color: #999;\r\n    text-decoration: line-through;\r\n  }\r\n\r\n  .mobile-current-price {\r\n    font-size: 16px;\r\n    font-weight: bold;\r\n    color: #2196f3;\r\n  }\r\n\r\n  /* 移动端 GPU 对比 */\r\n  .mobile-comparison-container {\r\n    overflow-x: auto;\r\n    -webkit-overflow-scrolling: touch;\r\n    margin: 0 -15px;\r\n    padding: 0 15px;\r\n  }\r\n\r\n  .mobile-comparison-scroll {\r\n    min-width: 600px;\r\n  }\r\n\r\n  .mobile-comparison-table {\r\n    width: 100%;\r\n    border-collapse: collapse;\r\n    font-size: 13px;\r\n  }\r\n\r\n  .mobile-comparison-table th,\r\n  .mobile-comparison-table td {\r\n    padding: 10px;\r\n    text-align: center;\r\n    border: 1px solid #eee;\r\n  }\r\n\r\n  .mobile-comparison-table thead {\r\n    background-color: #f5f5f5;\r\n  }\r\n\r\n  .mobile-comparison-table th {\r\n    font-weight: 600;\r\n    color: #333;\r\n  }\r\n\r\n  /* 移动端 核心优势 */\r\n  .mobile-services-list {\r\n    display: grid;\r\n    grid-template-columns: repeat(2, 1fr);\r\n    gap: 15px;\r\n  }\r\n\r\n  .mobile-service-card {\r\n    background-color: white;\r\n    border-radius: 8px;\r\n    padding: 15px;\r\n    box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n    text-align: center;\r\n  }\r\n\r\n  .mobile-service-icon {\r\n    font-size: 30px;\r\n    color: #2196f3;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .mobile-service-title {\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #333;\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .mobile-service-desc {\r\n    font-size: 12px;\r\n    color: #666;\r\n    line-height: 1.5;\r\n  }\r\n\r\n  /* 移动端 行业应用 */\r\n  .mobile-applications-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(2, 1fr);\r\n    gap: 15px;\r\n  }\r\n\r\n  .mobile-app-item {\r\n    position: relative;\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n    height: 120px;\r\n  }\r\n\r\n  .mobile-app-image {\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n\r\n  .mobile-app-image img {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n    transition: transform 0.5s ease;\r\n  }\r\n\r\n  .mobile-app-item:hover .mobile-app-image img {\r\n    transform: scale(1.05);\r\n  }\r\n\r\n  .mobile-app-title {\r\n    position: absolute;\r\n    bottom: 0;\r\n    left: 0;\r\n    right: 0;\r\n    padding: 10px;\r\n    background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);\r\n    color: white;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    margin: 0;\r\n  }\r\n\r\n  /* 移动端 咨询按钮 */\r\n  .mobile-consult-section {\r\n    background-color: #2196f3;\r\n    padding: 20px;\r\n    text-align: center;\r\n    margin: 30px -15px 0;\r\n  }\r\n\r\n  .mobile-consult-title {\r\n    color: white;\r\n    font-size: 18px;\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .mobile-consult-button {\r\n    background-color: white;\r\n    color: #2196f3;\r\n    border: none;\r\n    border-radius: 25px;\r\n    padding: 10px 25px;\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .mobile-consult-button:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 4px 8px rgba(0,0,0,0.1);\r\n  }\r\n\r\n  /* 移动端 联系弹窗 */\r\n  .mobile-contact-overlay {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background-color: rgba(0,0,0,0.5);\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    z-index: 1000;\r\n  }\r\n\r\n  .mobile-contact-modal {\r\n    position: relative;\r\n    background-color: white;\r\n    border-radius: 10px;\r\n    width: 80%;\r\n    max-width: 300px;\r\n    padding: 20px;\r\n    text-align: center;\r\n  }\r\n\r\n  .mobile-close-modal {\r\n    position: absolute;\r\n    top: 10px;\r\n    right: 15px;\r\n    font-size: 24px;\r\n    background: none;\r\n    border: none;\r\n    color: #999;\r\n    cursor: pointer;\r\n  }\r\n\r\n  .mobile-contact-content {\r\n    margin: 20px 0;\r\n  }\r\n\r\n  .mobile-contact-item {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-bottom: 15px;\r\n    font-size: 16px;\r\n  }\r\n\r\n  .mobile-contact-item i {\r\n    margin-right: 10px;\r\n    color: #2196f3;\r\n  }\r\n\r\n  .mobile-contact-note {\r\n    color: #666;\r\n    font-size: 14px;\r\n  }\r\n\r\n  /* 移动端动画 */\r\n  @keyframes mobile-fade {\r\n    from {\r\n      opacity: 0;\r\n      transform: translateY(20px);\r\n    }\r\n    to {\r\n      opacity: 1;\r\n      transform: translateY(0);\r\n    }\r\n  }\r\n\r\n  .mobile-fade-enter-active, .mobile-fade-leave-active {\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .mobile-fade-enter, .mobile-fade-leave-to {\r\n    opacity: 0;\r\n    transform: translateY(20px);\r\n  }\r\n}\r\n\r\n/* 响应式设计 - 平板 */\r\n@media (min-width: 769px) and (max-width: 1024px) {\r\n  .gpu-card-grid {\r\n    grid-template-columns: repeat(2, 1fr);\r\n  }\r\n\r\n\r\n\r\n  .services-grid {\r\n    grid-template-columns: repeat(2, 1fr);\r\n  }\r\n\r\n  .appsec-grid {\r\n    grid-template-columns: repeat(2, 1fr);\r\n  }\r\n\r\n  .appsec-wide {\r\n    grid-column: span 2;\r\n  }\r\n\r\n  .appsec-tall {\r\n    grid-column: span 1;\r\n    grid-row: span 1;\r\n    height: 180px;\r\n  }\r\n}\r\n\r\n\r\n\r\n</style>\r\n"], "mappings": ";AA2bA;AACA,OAAAA,MAAA;AACA,OAAAC,KAAA;AACA,OAAAC,MAAA;AACA,OAAAC,aAAA;AACA,SAAAC,WAAA,EAAAC,UAAA,EAAAC,WAAA,EAAAC,UAAA,EAAAC,YAAA;AAGA,SAAAC,QAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAX,MAAA;IAAAE,MAAA;IAAAD,KAAA;IAAAE;EAAA;EACAS,QAAA;IACAC,UAAA;MACA,aAAAC,UAAA;IACA;IACAC,QAAA;MACA,SAAAC,QAAA,CAAAC,MAAA,CAAAC,QAAA;IACA;IACAC,iBAAA;MACA,aAAAC,kBAAA;IACA;IACAC,mBAAA;MACA,QACA,KAAAC,YAAA,EACA,QAAAC,gBAAA,EACA,QAAAC,aAAA,EACA,QAAAC,iBAAA,EACA,KAAAC,YAAA,CACA;IACA;EACA;EACAC,KAAA;IACA;MACAC,WAAA;MACAC,SAAA;MACAC,cAAA;MAAA;MACAC,QAAA;MACAX,kBAAA;MACAY,gBAAA;MACAC,WAAA;QACAvB,IAAA;QACAwB,KAAA;MACA;MACAC,QAAA;MACAb,YAAA;QACAc,KAAA;QACAC,KAAA,EAAAC,OAAA;QACAC,KAAA;MACA;MACAhB,gBAAA,GACA;QACAa,KAAA;QACAC,KAAA,EAAAC,OAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAC,KAAA,EAAAC,OAAA;QACAC,KAAA;MACA,EACA;MACAf,aAAA,GACA;QACAY,KAAA;QACAC,KAAA,EAAAC,OAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAC,KAAA,EAAAC,OAAA;QACAC,KAAA;MACA,EACA;MACAd,iBAAA,GACA;QACAW,KAAA;QACAC,KAAA,EAAAC,OAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAC,KAAA,EAAAC,OAAA;QACAC,KAAA;MACA,EACA;MACAb,YAAA;QACAU,KAAA;QACAC,KAAA,EAAAC,OAAA;QACAC,KAAA;MACA;MACAC,YAAA,GACA;QACAC,GAAA,EAAAH,OAAA;QACAI,OAAA;UACAN,KAAA;UACAO,IAAA;UACAC,QAAA;UACAC,SAAA;UACAC,YAAA;QACA;MACA,GACA;QACAL,GAAA,EAAAH,OAAA;QACAI,OAAA;UACAN,KAAA;UACAO,IAAA;UACAC,QAAA;UACAG,aAAA;UACAC,gBAAA;UACAC,WAAA;UACAC,cAAA;QACA;MACA,GACA;QACAT,GAAA,EAAAH,OAAA;QACAI,OAAA;UACAN,KAAA;UACAO,IAAA;UACAC,QAAA;QACA;MACA,EACA;MACA9B,UAAA;MACAqC,KAAA;MACAC,OAAA;MACAC,aAAA;QACAC,IAAA;QACAC,QAAA;UACAC,KAAA;UACAC,oBAAA;QACA;QACAC,UAAA;UACAC,EAAA;UACAC,SAAA;QACA;QACAC,UAAA;UACAC,MAAA;UACAC,MAAA;QACA;MACA;MACAC,WAAA,GACA;QAAAC,EAAA;QAAAC,IAAA;QAAA9B,KAAA;QAAA+B,IAAA;MAAA,GACA;QAAAF,EAAA;QAAAC,IAAA;QAAA9B,KAAA;QAAA+B,IAAA;MAAA,GACA;QAAAF,EAAA;QAAAC,IAAA;QAAA9B,KAAA;QAAA+B,IAAA;MAAA,GACA;QAAAF,EAAA;QAAAC,IAAA;QAAA9B,KAAA;QAAA+B,IAAA;MAAA,GACA;QAAAF,EAAA;QAAAC,IAAA;QAAA9B,KAAA;QAAA+B,IAAA;MAAA,GACA;QAAAF,EAAA;QAAAC,IAAA;QAAA9B,KAAA;QAAA+B,IAAA;MAAA,EACA;MACAC,cAAA,GACA;QACA1D,IAAA;QACA2D,YAAA;QACAC,eAAA;QACAC,eAAA;QACAC,MAAA;QACAC,UAAA;QACAC,SAAA;MACA,GACA;QACAhE,IAAA;QACA2D,YAAA;QACAC,eAAA;QACAC,eAAA;QACAC,MAAA;QACAC,UAAA;QACAC,SAAA;MACA,GACA;QACAhE,IAAA;QACA2D,YAAA;QACAC,eAAA;QACAC,eAAA;QACAC,MAAA;QACAC,UAAA;QACAC,SAAA;MACA,GACA;QACAhE,IAAA;QACA2D,YAAA;QACAC,eAAA;QACAC,eAAA;QACAC,MAAA;QACAC,UAAA;QACAC,SAAA;MACA,GACA;QACAhE,IAAA;QACA2D,YAAA;QACAC,eAAA;QACAC,eAAA;QACAC,MAAA;QACAC,UAAA;QACAC,SAAA;MACA,GACA;QACAhE,IAAA;QACA2D,YAAA;QACAC,eAAA;QACAC,eAAA;QACAC,MAAA;QACAC,UAAA;QACAC,SAAA;MACA,GACA;QACAhE,IAAA;QACA2D,YAAA;QACAC,eAAA;QACAC,eAAA;QACAC,MAAA;QACAC,UAAA;QACAC,SAAA;MACA,EACA;MACAC,IAAA,GACA;QACAjE,IAAA;QACAkE,eAAA;QACAC,aAAA;QACAC,aAAA;QACAC,KAAA;QACAC,WAAA;QACAC,KAAA;MACA,GACA;QACAvE,IAAA;QACAkE,eAAA;QACAC,aAAA;QACAC,aAAA;QACAC,KAAA;QACAC,WAAA;QACAC,KAAA;MACA,GACA;QACAvE,IAAA;QACAkE,eAAA;QACAC,aAAA;QACAC,aAAA;QACAC,KAAA;QACAC,WAAA;QACAC,KAAA;MACA,GACA;QACAvE,IAAA;QACAkE,eAAA;QACAC,aAAA;QACAC,aAAA;QACAC,KAAA;QACAC,WAAA;QACAC,KAAA;MACA,GACA;QACAvE,IAAA;QACAkE,eAAA;QACAC,aAAA;QACAC,aAAA;QACAC,KAAA;QACAC,WAAA;QACAC,KAAA;MACA,GACA;QACAvE,IAAA;QACAkE,eAAA;QACAC,aAAA;QACAC,aAAA;QACAC,KAAA;QACAC,WAAA;QACAC,KAAA;MACA,GACA;QACAvE,IAAA;QACAkE,eAAA;QACAC,aAAA;QACAC,aAAA;QACAC,KAAA;QACAC,WAAA;QACAC,KAAA;MACA,GACA;QACAvE,IAAA;QACAkE,eAAA;QACAC,aAAA;QACAC,aAAA;QACAC,KAAA;QACAC,WAAA;QACAC,KAAA;MACA,EAEA;MACAC,WAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,oBAAA;IACA,MAAAC,GAAA,OAAAC,GAAA,CAAAC,MAAA,CAAAC,QAAA,CAAAC,IAAA;IACA,MAAAC,KAAA,GAAAL,GAAA,CAAAM,YAAA,CAAAC,GAAA;IAEA,IAAAF,KAAA;MACA,MAAAG,YAAA,GAAAC,YAAA,CAAAC,OAAA;MACA;MACA,KAAAF,YAAA;QACApF,QAAA,CAAAiF,KAAA;QACAI,YAAA,CAAAE,OAAA;QACA;QACA;MAEA;QACAT,MAAA,CAAAC,QAAA,CAAAC,IAAA,GAAAJ,GAAA,CAAAY,MAAA,GAAAZ,GAAA,CAAAa,QAAA;QACAJ,YAAA,CAAAE,OAAA;MACA;IACA;EACA;EACAG,QAAA;IACA,KAAAC,aAAA;IACAb,MAAA,CAAAc,gBAAA,gBAAAD,aAAA;;IAEA;IACA,KAAAE,eAAA,GAAAC,WAAA;MACA,KAAAC,IAAA;IACA;IAEA,KAAAC,cAAA,GAAAF,WAAA;MACA,KAAAG,UAAA;IACA;EACA;EACAC,cAAA;IACApB,MAAA,CAAAqB,mBAAA,gBAAAR,aAAA;IACAS,aAAA,MAAAP,eAAA;IACAO,aAAA,MAAAJ,cAAA;EAEA;EACAK,OAAA;IAEA,MAAA1B,qBAAA;MACA;QACA/E,UAAA,2BAAA0G,IAAA,CAAAC,GAAA;UACA,KAAArC,IAAA,GAAAqC,GAAA,CAAArF,IAAA,CAAAsF,IAAA,CAAAC,GAAA,CAAAC,IAAA;YACA,GAAAA,IAAA;YACAlC,KAAA,EAAAkC,IAAA,CAAAC,KAAA;YACApC,WAAA,EAAAmC,IAAA,CAAAnC,WAAA;UACA;UACA;QACA;MACA,SAAAqC,KAAA;QACAC,OAAA,CAAAD,KAAA,iBAAAA,KAAA;MACA;IACA;IAEAE,iBAAAC,CAAA;MACA,KAAA5F,WAAA,GAAA4F,CAAA,CAAAC,OAAA,IAAAC,OAAA;IACA;IAEAC,gBAAAH,CAAA;MACA,KAAA3F,SAAA,GAAA2F,CAAA,CAAAC,OAAA,IAAAC,OAAA;IACA;IAEAE,eAAA;MACA,UAAAhG,WAAA,UAAAC,SAAA;MAEA,MAAAgG,IAAA,QAAAjG,WAAA,QAAAC,SAAA;;MAEA;MACA,IAAAgG,IAAA,QAAA/F,cAAA;QACA,KAAA4E,UAAA;MACA;;MAEA;MACA,IAAAmB,IAAA,SAAA/F,cAAA;QACA,KAAAgG,UAAA;MACA;;MAEA;MACA,KAAAlG,WAAA;MACA,KAAAC,SAAA;IACA;IAEAiG,WAAA;MACA,KAAA1G,kBAAA,SAAAA,kBAAA,YAAAoB,YAAA,CAAAuF,MAAA,SAAAvF,YAAA,CAAAuF,MAAA;MACA,KAAAC,mBAAA;IACA;IAEAtB,WAAA;MACA,KAAAtF,kBAAA,SAAAA,kBAAA,aAAAoB,YAAA,CAAAuF,MAAA;MACA,KAAAC,mBAAA;IACA;IAEAA,oBAAA;MACAnB,aAAA,MAAAJ,cAAA;MACA,KAAAA,cAAA,GAAAF,WAAA;QACA,KAAAG,UAAA;MACA;IACA;IACAN,cAAA;MACA,KAAArE,QAAA,GAAAwD,MAAA,CAAA0C,UAAA;IACA;IACAC,iBAAA;MACA,KAAAlG,gBAAA;IACA;IACAmG,kBAAA;MACA,KAAAnG,gBAAA;IACA;IACAoG,mBAAAjB,IAAA;MACA,IAAAA,IAAA,CAAAzE,OAAA,CAAA2F,IAAA;QACA,KAAAC,OAAA,CAAAC,IAAA,CAAApB,IAAA,CAAAzE,OAAA,CAAA2F,IAAA;MACA;MACA,KAAAG,GAAA,CAAAC,KAAA,oBAAAtB,IAAA,CAAAzE,OAAA,CAAAN,KAAA;IACA;IACAsG,WAAAC,IAAA;MACA,SAAAC,WAAA,SAAAA,WAAA,KAAAD,IAAA;QACA,KAAAE,kBAAA,QAAAD,WAAA;QAEA,KAAAE,SAAA;UACA,MAAAC,QAAA,GAAA/H,QAAA,CAAAgI,gBAAA;UACAD,QAAA,CAAAE,OAAA,CAAAZ,IAAA;YACA,KAAAA,IAAA,CAAAa,SAAA,CAAAC,QAAA,cACAR,IAAA,iBAAAN,IAAA,CAAAa,SAAA,CAAAC,QAAA,kBACA,CAAAd,IAAA,CAAAa,SAAA,CAAAC,QAAA;cACAd,IAAA,CAAAa,SAAA,CAAAE,GAAA;cAEAC,UAAA;gBACAhB,IAAA,CAAAa,SAAA,CAAAI,MAAA;cACA;YACA;UACA;UAEA,KAAAV,WAAA,GAAAD,IAAA;QACA;MACA;QACA,KAAAC,WAAA,GAAAD,IAAA;MACA;MAEA,SAAAY,MAAA,CAAAZ,IAAA,KAAAA,IAAA;QACA,KAAAG,SAAA;UACAvD,MAAA,CAAAiE,QAAA;YACAC,GAAA;YACAC,QAAA;UACA;UACA,KAAApB,OAAA,CAAAqB,EAAA;QACA;MACA;QACA,KAAArB,OAAA,CAAAC,IAAA,CAAAI,IAAA;QACApD,MAAA,CAAAiE,QAAA;UACAC,GAAA;UACAC,QAAA;QACA;MACA;IACA;IACAE,KAAA;MACA,KAAA9I,UAAA,SAAAA,UAAA,YAAA0B,YAAA,CAAAuF,MAAA,SAAAvF,YAAA,CAAAuF,MAAA;MACA,KAAA5E,KAAA;IACA;IACAqD,KAAA;MACA,KAAA1F,UAAA,SAAAA,UAAA,aAAA0B,YAAA,CAAAuF,MAAA;MACA,KAAA5E,KAAA;IACA;IAEA0G,UAAAC,KAAA;MACA,KAAA1I,kBAAA,GAAA0I,KAAA;MACA;MACAjD,aAAA,MAAAJ,cAAA;MACA,KAAAA,cAAA,GAAAF,WAAA;QACA,KAAAG,UAAA;MACA;IACA;IACAqD,UAAAD,KAAA;MACA,KAAA3H,QAAA,GAAA2H,KAAA;IACA;IACAE,OAAAC,GAAA;MACA3C,OAAA,CAAA4C,GAAA,kBAAAD,GAAA,CAAAvJ,IAAA;IACA;IACAyJ,kBAAA;MACA7C,OAAA,CAAA4C,GAAA;IACA;IACAE,UAAA;MACA9C,OAAA,CAAA4C,GAAA;IACA;IACAG,UAAA;MACA,KAAAnF,WAAA,QAAAA,WAAA,YAAAA,WAAA,YAAAoF,SAAA,CAAAvC,MAAA;IACA;IACAwC,UAAA;MACA,KAAArF,WAAA,QAAAA,WAAA,QAAAoF,SAAA,CAAAvC,MAAA,YAAA7C,WAAA;IACA;IACAsF,UAAAP,GAAA;MACA3C,OAAA,CAAA4C,GAAA,kBAAAD,GAAA,CAAAvJ,IAAA;IACA;EACA;EACA+J,KAAA;IACA3J,WAAA4J,MAAA;MACA,MAAAC,IAAA,QAAAC,KAAA,CAAAC,gBAAA,EAAA7B,gBAAA;MACA,IAAA2B,IAAA;QACAA,IAAA,CAAA1B,OAAA,EAAA6B,GAAA,EAAAhB,KAAA;UACAgB,GAAA,CAAA5B,SAAA,CAAA6B,MAAA,WAAAjB,KAAA,KAAAY,MAAA;QACA;MACA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}